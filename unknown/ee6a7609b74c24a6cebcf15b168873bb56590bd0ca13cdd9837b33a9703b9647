# Log Monitoring Guide

## Overview

This guide provides comprehensive instructions for monitoring and analyzing log files in the Pharmacy Backend application during development. The solution includes real-time log streaming, filtering, formatting, and analysis tools.

## 🚀 Quick Start

### Basic Log Monitoring

```bash
# Monitor today's log file with pretty formatting
bun run logs:watch

# Monitor with development-friendly format (minimal noise)
bun run logs:dev

# Monitor all log files
bun run logs:watch:all
```

### Using the Shell Script

```bash
# Interactive monitoring
./scripts/logs.sh watch

# Development mode (cleaner output)
./scripts/logs.sh dev

# Show help
./scripts/logs.sh help
```

## 📊 Filtering and Searching

### By Log Level

```bash
# Show only errors (level >= 40)
bun run logs:filter:error
./scripts/logs.sh errors

# Show warnings and above (level >= 30)
bun run logs:filter:warn
./scripts/logs.sh warnings
```

### By Context

```bash
# Filter by specific service/context
./scripts/logs.sh context AuthService
./scripts/logs.sh context ProductsController
./scripts/logs.sh context PrismaService
```

### By Search Term

```bash
# Search for specific terms
./scripts/logs.sh search "database error"
./scripts/logs.sh search "validation failed"
./scripts/logs.sh search "user login"
```

## 🎯 Advanced Usage

### Interactive Log Monitor

```bash
# Start interactive mode with file selection
node scripts/log-monitor.js

# Monitor with specific filters
node scripts/log-monitor.js --level=40 --context=AuthService
node scripts/log-monitor.js --search="error" --file=pharmacy-backend-2025-06-18.log
```

### Pretty Print Static Files

```bash
# Pretty print today's log file
bun run logs:pretty

# Pretty print latest log file
bun run logs:pretty:latest

# Pretty print specific file
./scripts/logs.sh tail pharmacy-backend-2025-06-18.log
```

## 📁 File Management

### List Log Files

```bash
# List all available log files with details
./scripts/logs.sh list
```

### Log Statistics

```bash
# Show statistics for latest log file
./scripts/logs.sh stats
```

### Clean Old Logs

```bash
# Remove log files older than 7 days
bun run logs:clean
./scripts/logs.sh clean
```

## 🎨 Output Format

The log monitoring tools use `pino-pretty` with custom formatting:

- **Timestamp**: `yyyy-mm-dd HH:MM:ss` format
- **Level**: Color-coded and human-readable (TRACE, DEBUG, INFO, WARN, ERROR, FATAL)
- **Context**: Service/module name (e.g., AuthService, ProductsController)
- **Message**: Formatted log message
- **Data**: Additional structured data when available

### Example Output

```
2025-06-19 10:30:15 INFO  AuthService - User login successful
2025-06-19 10:30:16 DEBUG ProductsController - Fetching products with filters: {"category":"medicine","active":true}
2025-06-19 10:30:17 WARN  InventoryService - Low stock detected for product: PRD-001
2025-06-19 10:30:18 ERROR DatabaseService - Connection timeout after 5000ms
```

## 🔧 Configuration

### Pino-Pretty Configuration

The `.pino-prettyrc` file contains default formatting options:

```json
{
  "levelFirst": true,
  "colorize": true,
  "translateTime": "yyyy-mm-dd HH:MM:ss",
  "ignore": "pid,hostname",
  "messageFormat": "{context} - {msg}"
}
```

### Custom Log Levels

- **10 (TRACE)**: Function entry/exit, execution flow
- **20 (DEBUG)**: Application state, variable values
- **30 (INFO)**: Business events, successful operations
- **40 (WARN)**: Potential issues, deprecation warnings
- **50 (ERROR)**: Operation failures, handled exceptions
- **60 (FATAL)**: Unrecoverable errors, application crashes

## 🛠️ Available Scripts

### Package.json Scripts

```json
{
  "logs:watch": "tail -f logs/pharmacy-backend-$(date +%Y-%m-%d).log | pino-pretty",
  "logs:watch:all": "tail -f logs/*.log | pino-pretty",
  "logs:pretty": "pino-pretty < logs/pharmacy-backend-$(date +%Y-%m-%d).log",
  "logs:pretty:latest": "ls -t logs/*.log | head -1 | xargs pino-pretty <",
  "logs:filter:error": "tail -f logs/pharmacy-backend-$(date +%Y-%m-%d).log | pino-pretty --levelFirst --colorize --translateTime --search 'level>=40'",
  "logs:filter:warn": "tail -f logs/pharmacy-backend-$(date +%Y-%m-%d).log | pino-pretty --levelFirst --colorize --translateTime --search 'level>=30'",
  "logs:filter:context": "tail -f logs/pharmacy-backend-$(date +%Y-%m-%d).log | pino-pretty --levelFirst --colorize --translateTime --search",
  "logs:dev": "tail -f logs/pharmacy-backend-$(date +%Y-%m-%d).log | pino-pretty --levelFirst --colorize --translateTime --ignore 'pid,hostname'",
  "logs:clean": "find logs -name '*.log' -mtime +7 -delete && echo 'Cleaned logs older than 7 days'"
}
```

### Shell Script Commands

```bash
./scripts/logs.sh watch          # Monitor today's log
./scripts/logs.sh watch-all      # Monitor all logs
./scripts/logs.sh dev            # Development mode
./scripts/logs.sh errors         # Error level only
./scripts/logs.sh warnings       # Warning level and above
./scripts/logs.sh context <name> # Filter by context
./scripts/logs.sh search <term>  # Search for term
./scripts/logs.sh pretty         # Pretty print today's log
./scripts/logs.sh latest         # Pretty print latest log
./scripts/logs.sh list           # List all log files
./scripts/logs.sh stats          # Show log statistics
./scripts/logs.sh clean          # Clean old logs
./scripts/logs.sh help           # Show help
```

## 💡 Development Tips

### 1. Real-time Development Monitoring

For active development, use:
```bash
bun run logs:dev
```
This provides clean, real-time output without noise.

### 2. Debugging Specific Issues

When debugging authentication issues:
```bash
./scripts/logs.sh context AuthService
```

When looking for database problems:
```bash
./scripts/logs.sh search "database\|prisma\|connection"
```

### 3. Performance Monitoring

Monitor warnings and errors during load testing:
```bash
./scripts/logs.sh warnings
```

### 4. Log Analysis

Get overview of application activity:
```bash
./scripts/logs.sh stats
```

## 🔍 Troubleshooting

### No Log Files Found

1. Check if the backend is running and generating logs
2. Verify the log directory exists: `ls -la logs/`
3. Check environment configuration in `.env.development`

### Pino-Pretty Not Working

1. Ensure pino-pretty is installed: `bun install`
2. Check if the command exists: `which pino-pretty`
3. Try using the Node.js script: `node scripts/log-monitor.js`

### Permission Issues

Make sure the shell script is executable:
```bash
chmod +x scripts/logs.sh
```

### Large Log Files

For very large log files, use filtering to reduce output:
```bash
./scripts/logs.sh errors  # Only show errors
./scripts/logs.sh search "specific-term"  # Search for specific content
```

## 🚀 Integration with Development Workflow

### VS Code Integration

Add to VS Code tasks.json:
```json
{
  "label": "Monitor Logs",
  "type": "shell",
  "command": "bun run logs:dev",
  "group": "build",
  "presentation": {
    "echo": true,
    "reveal": "always",
    "focus": false,
    "panel": "new"
  }
}
```

### Terminal Multiplexer

Use with tmux or screen for persistent monitoring:
```bash
# Create new tmux session for logs
tmux new-session -d -s logs 'bun run logs:dev'

# Attach to logs session
tmux attach-session -t logs
```

This comprehensive log monitoring solution provides everything you need for efficient development debugging and monitoring of your Pharmacy Backend application.
