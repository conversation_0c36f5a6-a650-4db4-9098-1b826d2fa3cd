import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { SettingsService } from '../../settings/settings.service';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class ReferenceGeneratorService {
  constructor(
    private prisma: PrismaService,
    private settingsService: SettingsService,
    private readonly logger: PinoLogger,
  ) {}

  /**
   * Generate payment reference number in format: PAY-{PHARMACY_INITIALS}SUP-DDMMYY-{SEQUENCE}
   * Example: PAY-ASMSUP-100624-001
   */
  async generatePaymentReference(): Promise<string> {
    this.logger.trace('Entering generatePaymentReference method');

    try {
      // Get pharmacy name from settings
      this.logger.trace('Fetching pharmacy name from settings');
      const pharmacyName = await this.getPharmacyName();

      // Generate pharmacy initials
      const pharmacyInitials = this.generatePharmacyInitials(pharmacyName);
      this.logger.debug({
        pharmacyName,
        pharmacyInitials
      }, 'Generated pharmacy initials');

      // Get current date in DDMMYY format
      const dateString = this.formatDateDDMMYY(new Date());
      this.logger.debug({ dateString }, 'Formatted date for reference');

      // Get next sequence number for today
      this.logger.debug({ dateString }, 'Getting next sequence number');
      const sequence = await this.getNextSequenceNumber(dateString);

      // Construct reference number
      const reference = `PAY-${pharmacyInitials}SUP-${dateString}-${sequence.toString().padStart(3, '0')}`;

      this.logger.info({
        reference,
        pharmacyInitials,
        dateString,
        sequence
      }, 'Payment reference generated successfully');

      this.logger.trace({ reference }, 'Exiting generatePaymentReference method');
      return reference;
    } catch (error) {
      this.logger.error({
        err: error
      }, 'Error generating payment reference, using fallback');

      // Fallback reference with timestamp
      const fallbackDate = this.formatDateDDMMYY(new Date());
      const timestamp = Date.now().toString().slice(-3);
      const fallbackReference = `PAY-APTSUP-${fallbackDate}-${timestamp}`;

      this.logger.warn({
        fallbackReference,
        fallbackDate,
        timestamp
      }, 'Using fallback payment reference');

      return fallbackReference;
    }
  }

  /**
   * Validate if a reference number is unique
   */
  async validateReferenceUniqueness(reference: string): Promise<boolean> {
    this.logger.trace({ reference }, 'Entering validateReferenceUniqueness method');

    try {
      this.logger.debug({ reference }, 'Checking reference uniqueness');

      const existingPayment = await this.prisma.supplierPayment.findFirst({
        where: { reference },
      });

      return !existingPayment;
    } catch (error) {
      this.logger.error({
        err: error,
        reference
      }, 'Failed to validate reference uniqueness');
      throw error;
    }
  }

  /**
   * Get pharmacy name from settings with fallback
   */
  private async getPharmacyName(): Promise<string> {
    try {
      const pharmacyName = await this.settingsService.getSetting('pharmacyName');
      return pharmacyName || 'Apotek';
    } catch (error) {
      this.logger.warn('Could not fetch pharmacy name from settings:', error);
      return 'Apotek';
    }
  }

  /**
   * Generate pharmacy initials from pharmacy name
   * Example: "Apotek Sehat Mandiri" -> "ASM"
   */
  private generatePharmacyInitials(pharmacyName: string): string {
    try {
      // Split by spaces and take first letter of each word
      const words = pharmacyName.trim().split(/\s+/);
      const initials = words
        .map(word => word.charAt(0).toUpperCase())
        .join('');
      
      // Ensure we have at least 2 characters, max 4
      if (initials.length === 0) {
        return 'APT';
      } else if (initials.length === 1) {
        return initials + 'PT';
      } else if (initials.length > 4) {
        return initials.substring(0, 4);
      }
      
      return initials;
    } catch (error) {
      this.logger.warn('Error generating pharmacy initials:', error);
      return 'APT';
    }
  }

  /**
   * Format date to DDMMYY format
   * Example: June 10, 2024 -> "100624"
   */
  private formatDateDDMMYY(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString().slice(-2);
    
    return `${day}${month}${year}`;
  }

  /**
   * Get next sequence number for the given date
   * Sequence resets to 001 each day
   */
  private async getNextSequenceNumber(dateString: string): Promise<number> {
    try {
      // Find all payments created today with reference numbers containing the date
      const todayPayments = await this.prisma.supplierPayment.findMany({
        where: {
          reference: {
            contains: `-${dateString}-`,
          },
        },
        select: {
          reference: true,
        },
      });

      // Extract sequence numbers from existing references
      const sequences = todayPayments
        .map(payment => {
          if (!payment.reference) return 0;
          
          // Extract sequence from reference like "PAY-ASMSUP-100624-001"
          const match = payment.reference.match(/-(\d{3})$/);
          return match ? parseInt(match[1], 10) : 0;
        })
        .filter(seq => seq > 0);

      // Return next sequence number
      const maxSequence = sequences.length > 0 ? Math.max(...sequences) : 0;
      return maxSequence + 1;
    } catch (error) {
      this.logger.warn('Error getting next sequence number:', error);
      // Fallback to timestamp-based sequence
      return parseInt(Date.now().toString().slice(-3)) % 1000;
    }
  }

  /**
   * Generate a new reference if the provided one is not unique
   */
  async ensureUniqueReference(proposedReference?: string): Promise<string> {
    // If no reference provided, generate a new one
    if (!proposedReference) {
      return this.generatePaymentReference();
    }

    // If provided reference is unique, use it
    const isUnique = await this.validateReferenceUniqueness(proposedReference);
    if (isUnique) {
      return proposedReference;
    }

    // If not unique, generate a new one
    this.logger.warn(`Reference ${proposedReference} is not unique, generating new one`);
    return this.generatePaymentReference();
  }
}
