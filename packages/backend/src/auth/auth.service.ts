import {
  Injectable,
  ConflictException,
  UnauthorizedException,
  BadRequestException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import * as bcrypt from 'bcrypt';
import { PrismaService } from '../prisma/prisma.service';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { UpdateProfileDto } from './dto/update-profile.dto';

@Injectable()
export class AuthService {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    @InjectPinoLogger(AuthService.name)
    private readonly logger: PinoLogger,
  ) {}

  async register(registerDto: RegisterDto) {
    const { email, password, firstName, lastName, avatar, dateOfBirth, phoneNumber, address, role } = registerDto;

    try {
      // Check if user already exists
      const existingUser = await this.prisma.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        throw new ConflictException('Pengguna dengan email ini sudah ada');
      }

      // Validate password strength
      if (password.length < 8) {
        throw new BadRequestException('Kata sandi minimal 8 karakter');
      }

      // Hash password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(password, saltRounds);

      // Create user
      const user = await this.prisma.user.create({
        data: {
          email,
          password: hashedPassword,
          firstName,
          lastName,
          avatar,
          dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,
          phoneNumber,
          address,
          role: role || 'CASHIER',
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          avatar: true,
          dateOfBirth: true,
          phoneNumber: true,
          address: true,
          role: true,
          createdAt: true,
        },
      });

      // Generate JWT token
      const payload = { sub: user.id, email: user.email };
      const accessToken = this.jwtService.sign(payload);

      return {
        user,
        accessToken,
      };
    } catch (error) {
      this.logger.error('User registration failed', error, {
        email,
        role: role || 'CASHIER',
        errorType: error.constructor.name,
        errorMessage: error.message,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  async login(loginDto: LoginDto) {
    const { email, password } = loginDto;

    try {
      // Find user by email
      const user = await this.prisma.user.findUnique({
        where: { email },
      });

      if (!user) {
        throw new UnauthorizedException('Kredensial tidak valid');
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        throw new UnauthorizedException('Kredensial tidak valid');
      }

      // Update last login
      await this.updateLastLogin(user.id);

      // Generate JWT token
      const payload = { sub: user.id, email: user.email, role: user.role };
      const accessToken = this.jwtService.sign(payload);

      return {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          avatar: user.avatar,
          dateOfBirth: user.dateOfBirth,
          phoneNumber: user.phoneNumber,
          address: user.address,
          role: user.role,
          createdAt: user.createdAt,
        },
        accessToken,
      };
    } catch (error) {
      this.logger.error('User login failed', error, {
        email,
        errorType: error.constructor.name,
        errorMessage: error.message,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  async validateUser(email: string, password: string): Promise<any> {
    try {
      const user = await this.prisma.user.findUnique({
        where: { email },
      });

      if (user && (await bcrypt.compare(password, user.password))) {
        const { password: _, ...result } = user;
        return result;
      }
      return null;
    } catch (error) {
      this.logger.error('User validation error', error, {
        email,
        errorType: error.constructor.name,
      });
      return null;
    }
  }

  async findUserById(id: string) {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          avatar: true,
          dateOfBirth: true,
          phoneNumber: true,
          address: true,
          role: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      return user;
    } catch (error) {
      this.logger.error('Error finding user by ID', error, {
        userId: id,
        errorType: error.constructor.name,
      });
      throw error;
    }
  }

  async updateProfile(id: string, updateProfileDto: UpdateProfileDto) {
    const { firstName, lastName, avatar, dateOfBirth, phoneNumber, address } = updateProfileDto;

    try {
      const updatedUser = await this.prisma.user.update({
        where: { id },
        data: {
          firstName,
          lastName,
          avatar,
          dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined,
          phoneNumber,
          address,
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          avatar: true,
          dateOfBirth: true,
          phoneNumber: true,
          address: true,
          role: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      return updatedUser;
    } catch (error) {
      this.logger.error('User profile update failed', error, {
        userId: id,
        updateData: {
          firstName: !!firstName,
          lastName: !!lastName,
          avatar: !!avatar,
          dateOfBirth: !!dateOfBirth,
          phoneNumber: !!phoneNumber,
          address: !!address,
        },
        errorType: error.constructor.name,
        errorMessage: error.message,
        timestamp: new Date().toISOString(),
      });
      throw new BadRequestException(`Gagal memperbarui profil: ${error.message}`);
    }
  }

  async updateLastLogin(id: string) {
    try {
      await this.prisma.user.update({
        where: { id },
        data: { lastLoginAt: new Date() },
      });
    } catch (error) {
      this.logger.error('Failed to update last login timestamp', error, {
        userId: id,
        errorType: error.constructor.name,
        errorMessage: error.message,
      });
      throw new BadRequestException(`Gagal memperbarui terakhir login: ${error.message}`);
    }
  }
}
