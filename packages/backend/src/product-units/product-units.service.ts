import { Injectable } from '@nestjs/common';
import { Inject<PERSON>inoLogger, PinoLogger } from 'nestjs-pino';
import { PrismaService } from '../prisma/prisma.service';
import { ProductUnitQueryDto } from './dto/product-unit-query.dto';
import { Prisma, UnitType } from '@prisma/client';

@Injectable()
export class ProductUnitsService {
  constructor(
    private readonly prisma: PrismaService,
    @InjectPinoLogger(ProductUnitsService.name)
    private readonly logger: PinoLogger,
  ) { }

  async findAll(query: ProductUnitQueryDto) {
    const {
      search,
      type,
      isActive,
      isBaseUnit,
      sortBy = 'name',
      sortOrder = 'asc'
    } = query;

    // Build where clause
    const where: Prisma.ProductUnitWhereInput = {};

    // Search functionality
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { abbreviation: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Filters
    if (type) where.type = type;
    if (isActive !== undefined) where.isActive = isActive;
    if (isBaseUnit !== undefined) where.isBaseUnit = isBaseUnit;

    // Sorting
    const getOrderByField = (field: string): Prisma.ProductUnitOrderByWithRelationInput => {
      const orderDirection = sortOrder === 'asc' ? 'asc' : 'desc';

      switch (field) {
        case 'name':
          return { name: orderDirection };
        case 'abbreviation':
          return { abbreviation: orderDirection };
        case 'type':
          return { type: orderDirection };
        case 'isBaseUnit':
          return { isBaseUnit: orderDirection };
        case 'createdAt':
          return { createdAt: orderDirection };
        case 'updatedAt':
          return { updatedAt: orderDirection };
        default:
          return { name: orderDirection };
      }
    };

    const orderBy = getOrderByField(sortBy);

    try {
      return await this.prisma.productUnit.findMany({
        where,
        orderBy,
      });
    } catch (error) {
      this.logger.error({ err: error, query }, 'Failed to retrieve product units');
      throw error;
    }
  }

  async findBaseUnits() {
    try {
      return await this.prisma.productUnit.findMany({
        where: {
          isBaseUnit: true,
          isActive: true,
        },
        orderBy: { name: 'asc' },
      });
    } catch (error) {
      this.logger.error({ err: error }, 'Failed to retrieve base units');
      throw error;
    }
  }

  async findByType(type?: string) {
    const where: Prisma.ProductUnitWhereInput = {
      isActive: true,
    };

    if (type && Object.values(UnitType).includes(type as UnitType)) {
      where.type = type as UnitType;
      this.logger.debug({ type }, 'Filtering by unit type');
    } else if (type) {
      this.logger.warn({ type, validTypes: Object.values(UnitType) }, 'Invalid unit type provided, ignoring filter');
    }

    try {
      return await this.prisma.productUnit.findMany({
        where,
        orderBy: { name: 'asc' },
      });
    } catch (error) {
      this.logger.error({ err: error, type }, 'Failed to retrieve units by type');
      throw error;
    }
  }

  async findOne(id: string) {
    try {
      const unit = await this.prisma.productUnit.findUnique({
        where: { id },
      });

      if (!unit) {
        this.logger.warn({ unitId: id }, 'Product unit not found');
      }

      this.logger.trace({ unitId: id }, 'Exiting findOne product unit method');
      return unit;
    } catch (error) {
      this.logger.error({ err: error, unitId: id }, 'Failed to retrieve product unit');
      throw error;
    }
  }

  async getStats() {
    try {
      const [
        total,
        active,
        inactive,
        baseUnits,
        byType,
      ] = await Promise.all([
        this.prisma.productUnit.count(),
        this.prisma.productUnit.count({ where: { isActive: true } }),
        this.prisma.productUnit.count({ where: { isActive: false } }),
        this.prisma.productUnit.count({ where: { isBaseUnit: true, isActive: true } }),
        this.prisma.productUnit.groupBy({
          by: ['type'],
          _count: { type: true },
          where: { isActive: true },
        }),
      ]);

      return {
        total,
        active,
        inactive,
        baseUnits,
        byType: byType.reduce((acc, item) => {
          acc[item.type] = item._count.type;
          return acc;
        }, {}),
      };
    } catch (error) {
      this.logger.error({ err: error }, 'Failed to retrieve product unit statistics');
      throw error;
    }
  }
}
