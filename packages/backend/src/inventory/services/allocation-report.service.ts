import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import {
  AllocationReportRequestDto,
  AllocationReportResponse,
  ReportFormat,
  ReportLanguage
} from '../dto/allocation-report.dto';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import * as PDFDocument from 'pdfkit';
import * as XLSX from 'xlsx';
import { PinoLogger, InjectPinoLogger } from 'nestjs-pino';

@Injectable()
export class AllocationReportService {
  private readonly reportsDir = path.join(process.cwd(), 'temp', 'reports');

  constructor(
    private prisma: PrismaService,
    @InjectPinoLogger(AllocationReportService.name)
    private readonly logger: PinoLogger,
  ) {
    // Ensure reports directory exists
    if (!fs.existsSync(this.reportsDir)) {
      fs.mkdirSync(this.reportsDir, { recursive: true });
    }
  }

  async generateReport(request: AllocationReportRequestDto): Promise<AllocationReportResponse> {
    const reportId = uuidv4();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

    let fileName: string;
    let fileContent: Buffer;

    switch (request.format) {
      case ReportFormat.PDF:
        fileName = `Laporan_Alokasi_${timestamp}.pdf`;
        fileContent = await this.generatePDFReport(request, reportId);
        break;
      case ReportFormat.EXCEL:
        fileName = `Laporan_Alokasi_${timestamp}.xlsx`;
        fileContent = await this.generateExcelReport(request, reportId);
        break;
      case ReportFormat.CSV:
        fileName = `Laporan_Alokasi_${timestamp}.csv`;
        fileContent = await this.generateCSVReport(request, reportId);
        break;
      default:
        throw new BadRequestException('Format laporan tidak didukung');
    }

    // Save file to temporary directory
    const filePath = path.join(this.reportsDir, fileName);
    fs.writeFileSync(filePath, fileContent);

    // Calculate expiration time (1 hour from now)
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 1);

    // Create download URL (this would be served by a static file endpoint)
    const reportUrl = `/api/reports/download/${fileName}`;

    return {
      reportUrl,
      reportId,
      fileName,
      fileSize: fileContent.length,
      expiresAt: expiresAt.toISOString(),
      format: request.format,
    };
  }

  private async generatePDFReport(request: AllocationReportRequestDto, reportId: string): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      try {
        const { allocationResult, userInfo, pharmacyInfo, language } = request;
        const isIndonesian = language === ReportLanguage.ID;

        // Create PDF document
        const doc = new PDFDocument({
          size: 'A4',
          margin: 50,
          info: {
            Title: isIndonesian ? 'Laporan Alokasi Stok' : 'Stock Allocation Report',
            Author: userInfo?.name || 'Pharmacy System',
            Subject: isIndonesian ? 'Laporan Alokasi Stok Apotek' : 'Pharmacy Stock Allocation Report',
            Creator: 'Pharmacy Management System',
            Producer: 'Pharmacy Management System',
          }
        });

        const chunks: Buffer[] = [];
        doc.on('data', (chunk) => chunks.push(chunk));
        doc.on('end', () => resolve(Buffer.concat(chunks)));
        doc.on('error', reject);

        // Header
        this.addPDFHeader(doc, pharmacyInfo, isIndonesian);

        // Title
        doc.fontSize(18)
           .font('Helvetica-Bold')
           .text(isIndonesian ? 'LAPORAN ALOKASI STOK' : 'STOCK ALLOCATION REPORT', { align: 'center' })
           .moveDown();

        // Report info
        doc.fontSize(10)
           .font('Helvetica')
           .text(`${isIndonesian ? 'ID Laporan' : 'Report ID'}: ${reportId}`, { align: 'right' })
           .text(`${isIndonesian ? 'Tanggal' : 'Date'}: ${new Date().toLocaleDateString(isIndonesian ? 'id-ID' : 'en-US')}`, { align: 'right' })
           .moveDown();

        // Allocation Summary
        this.addPDFAllocationSummary(doc, allocationResult, isIndonesian);

        // Batch Details
        if (allocationResult.batches && allocationResult.batches.length > 0) {
          this.addPDFBatchDetails(doc, allocationResult.batches, isIndonesian);
        }

        // Warnings and Errors
        this.addPDFWarningsAndErrors(doc, allocationResult, isIndonesian);

        // Footer
        this.addPDFFooter(doc, userInfo, isIndonesian);

        doc.end();
      } catch (error) {
        this.logger.error({ err: error, reportId, request }, `Failed to generate PDF report: ${error.message}`);
        reject(error);
      }
    });
  }

  private async generateExcelReport(request: AllocationReportRequestDto, reportId: string): Promise<Buffer> {
    try {
      const { allocationResult, userInfo, pharmacyInfo, language } = request;
      const isIndonesian = language === ReportLanguage.ID;

      // Create workbook
      const workbook = XLSX.utils.book_new();

      // Summary Sheet
      const summaryData = this.createExcelSummarySheet(allocationResult, userInfo, pharmacyInfo, isIndonesian, reportId);
      const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
      XLSX.utils.book_append_sheet(workbook, summarySheet, isIndonesian ? 'Ringkasan' : 'Summary');

      // Batch Details Sheet
      if (allocationResult.batches && allocationResult.batches.length > 0) {
        const batchData = this.createExcelBatchSheet(allocationResult.batches, isIndonesian);
        const batchSheet = XLSX.utils.aoa_to_sheet(batchData);
        XLSX.utils.book_append_sheet(workbook, batchSheet, isIndonesian ? 'Detail Batch' : 'Batch Details');
      }

      // Compliance Sheet
      const complianceData = this.createExcelComplianceSheet(allocationResult, userInfo, pharmacyInfo, isIndonesian);
      const complianceSheet = XLSX.utils.aoa_to_sheet(complianceData);
      XLSX.utils.book_append_sheet(workbook, complianceSheet, isIndonesian ? 'Kepatuhan' : 'Compliance');

      // Generate buffer
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      return Buffer.from(buffer);
    } catch (error) {
      this.logger.error({ err: error, reportId, request }, `Failed to generate Excel report: ${error.message}`);
      throw new BadRequestException(`Gagal membuat laporan Excel: ${error.message}`);
    }
  }

  private async generateCSVReport(request: AllocationReportRequestDto, reportId: string): Promise<Buffer> {
    try {
      const { allocationResult, userInfo, pharmacyInfo, language } = request;
      const isIndonesian = language === ReportLanguage.ID;

      // Create comprehensive CSV data
      const csvData: string[] = [];

      // Header information
      csvData.push(`# ${isIndonesian ? 'LAPORAN ALOKASI STOK' : 'STOCK ALLOCATION REPORT'}`);
      csvData.push(`# ${isIndonesian ? 'ID Laporan' : 'Report ID'}: ${reportId}`);
      csvData.push(`# ${isIndonesian ? 'Tanggal' : 'Date'}: ${new Date().toLocaleDateString(isIndonesian ? 'id-ID' : 'en-US')}`);

      if (pharmacyInfo) {
        csvData.push(`# ${isIndonesian ? 'Apotek' : 'Pharmacy'}: ${pharmacyInfo.name}`);
        csvData.push(`# ${isIndonesian ? 'Alamat' : 'Address'}: ${pharmacyInfo.address}`);
        csvData.push(`# ${isIndonesian ? 'Nomor Izin' : 'License'}: ${pharmacyInfo.licenseNumber}`);
      }

      csvData.push(''); // Empty line

      // Summary section
      csvData.push(`# ${isIndonesian ? 'RINGKASAN ALOKASI' : 'ALLOCATION SUMMARY'}`);
      csvData.push(`${isIndonesian ? 'Jumlah Diminta' : 'Requested Quantity'},${allocationResult.requestedQuantity}`);
      csvData.push(`${isIndonesian ? 'Jumlah Dialokasikan' : 'Allocated Quantity'},${allocationResult.allocatedQuantity}`);
      csvData.push(`${isIndonesian ? 'Metode' : 'Method'},${allocationResult.method}`);
      csvData.push(`${isIndonesian ? 'Total Biaya' : 'Total Cost'},${allocationResult.totalCost}`);
      csvData.push(`${isIndonesian ? 'Status' : 'Status'},${allocationResult.success ? (isIndonesian ? 'Berhasil' : 'Success') : (isIndonesian ? 'Gagal' : 'Failed')}`);
      csvData.push(''); // Empty line

      // Batch details
      if (allocationResult.batches && allocationResult.batches.length > 0) {
        csvData.push(`# ${isIndonesian ? 'DETAIL BATCH' : 'BATCH DETAILS'}`);

        const headers = isIndonesian
          ? ['Nomor Batch', 'Dialokasikan', 'Tersedia', 'Harga Satuan', 'Total Biaya', 'Tanggal Kedaluwarsa', 'Lokasi']
          : ['Batch Number', 'Allocated Quantity', 'Available Quantity', 'Cost Price', 'Total Cost', 'Expiry Date', 'Location'];

        csvData.push(headers.join(','));

        allocationResult.batches.forEach((batch: any) => {
          const row = [
            this.escapeCsvValue(batch.batchNumber || 'N/A'),
            batch.allocatedQuantity,
            batch.availableQuantity,
            batch.costPrice,
            batch.costPrice * batch.allocatedQuantity,
            batch.expiryDate ? new Date(batch.expiryDate).toLocaleDateString(isIndonesian ? 'id-ID' : 'en-US') : 'N/A',
            this.escapeCsvValue(batch.location || 'N/A')
          ];
          csvData.push(row.join(','));
        });
        csvData.push(''); // Empty line
      }

      // Warnings and errors
      if (allocationResult.warnings && allocationResult.warnings.length > 0) {
        csvData.push(`# ${isIndonesian ? 'PERINGATAN' : 'WARNINGS'}`);
        allocationResult.warnings.forEach((warning: string) => {
          csvData.push(`"${warning}"`);
        });
        csvData.push(''); // Empty line
      }

      if (allocationResult.errors && allocationResult.errors.length > 0) {
        csvData.push(`# ${isIndonesian ? 'ERROR' : 'ERRORS'}`);
        allocationResult.errors.forEach((error: string) => {
          csvData.push(`"${error}"`);
        });
        csvData.push(''); // Empty line
      }

      // Audit information
      if (userInfo) {
        csvData.push(`# ${isIndonesian ? 'INFORMASI AUDIT' : 'AUDIT INFORMATION'}`);
        csvData.push(`${isIndonesian ? 'Dibuat oleh' : 'Generated by'},${this.escapeCsvValue(userInfo.name)} (${userInfo.role})`);
        csvData.push(`${isIndonesian ? 'Waktu Pembuatan' : 'Generation Time'},${new Date().toLocaleString(isIndonesian ? 'id-ID' : 'en-US')}`);
      }

      // Join all data with newlines and encode as UTF-8
      const csvContent = csvData.join('\n');
      return Buffer.from('\ufeff' + csvContent, 'utf8'); // Add BOM for proper UTF-8 encoding
    } catch (error) {
      this.logger.error({ err: error, reportId, request }, `Failed to generate CSV report: ${error.message}`);
      throw new BadRequestException(`Failed to generate CSV report: ${error.message}`);
    }
  }



  // Excel Helper Methods
  private createExcelSummarySheet(allocationResult: any, userInfo: any, pharmacyInfo: any, isIndonesian: boolean, reportId: string): any[][] {
    const data: any[][] = [];

    // Title
    data.push([isIndonesian ? 'LAPORAN ALOKASI STOK' : 'STOCK ALLOCATION REPORT']);
    data.push([]);

    // Report Info
    data.push([isIndonesian ? 'ID Laporan' : 'Report ID', reportId]);
    data.push([isIndonesian ? 'Tanggal' : 'Date', new Date().toLocaleDateString(isIndonesian ? 'id-ID' : 'en-US')]);
    data.push([]);

    // Pharmacy Info
    if (pharmacyInfo) {
      data.push([isIndonesian ? 'INFORMASI APOTEK' : 'PHARMACY INFORMATION']);
      data.push([isIndonesian ? 'Nama' : 'Name', pharmacyInfo.name]);
      data.push([isIndonesian ? 'Alamat' : 'Address', pharmacyInfo.address]);
      data.push([isIndonesian ? 'Nomor Izin' : 'License Number', pharmacyInfo.licenseNumber]);
      data.push([isIndonesian ? 'Apoteker Penanggung Jawab' : 'Pharmacist in Charge', pharmacyInfo.pharmacistName]);
      data.push([]);
    }

    // Allocation Summary
    data.push([isIndonesian ? 'RINGKASAN ALOKASI' : 'ALLOCATION SUMMARY']);
    data.push([isIndonesian ? 'Jumlah Diminta' : 'Requested Quantity', allocationResult.requestedQuantity]);
    data.push([isIndonesian ? 'Jumlah Dialokasikan' : 'Allocated Quantity', allocationResult.allocatedQuantity]);
    data.push([isIndonesian ? 'Metode' : 'Method', allocationResult.method]);
    data.push([isIndonesian ? 'Total Biaya' : 'Total Cost', allocationResult.totalCost]);
    data.push([isIndonesian ? 'Status' : 'Status', allocationResult.success ? (isIndonesian ? 'Berhasil' : 'Success') : (isIndonesian ? 'Gagal' : 'Failed')]);
    data.push([]);

    // Audit Info
    if (userInfo) {
      data.push([isIndonesian ? 'INFORMASI AUDIT' : 'AUDIT INFORMATION']);
      data.push([isIndonesian ? 'Dibuat oleh' : 'Generated by', `${userInfo.name} (${userInfo.role})`]);
      data.push([isIndonesian ? 'Waktu Pembuatan' : 'Generation Time', new Date().toLocaleString(isIndonesian ? 'id-ID' : 'en-US')]);
    }

    return data;
  }

  private createExcelBatchSheet(batches: any[], isIndonesian: boolean): any[][] {
    const data: any[][] = [];

    // Headers
    const headers = isIndonesian
      ? ['Nomor Batch', 'Dialokasikan', 'Tersedia', 'Harga Satuan', 'Total Biaya', 'Tanggal Kedaluwarsa', 'Lokasi']
      : ['Batch Number', 'Allocated Quantity', 'Available Quantity', 'Cost Price', 'Total Cost', 'Expiry Date', 'Location'];

    data.push(headers);

    // Batch data
    batches.forEach((batch) => {
      data.push([
        batch.batchNumber || 'N/A',
        batch.allocatedQuantity,
        batch.availableQuantity,
        batch.costPrice,
        batch.costPrice * batch.allocatedQuantity,
        batch.expiryDate ? new Date(batch.expiryDate).toLocaleDateString(isIndonesian ? 'id-ID' : 'en-US') : 'N/A',
        batch.location || 'N/A'
      ]);
    });

    return data;
  }

  private createExcelComplianceSheet(allocationResult: any, _userInfo: any, _pharmacyInfo: any, isIndonesian: boolean): any[][] {
    const data: any[][] = [];

    data.push([isIndonesian ? 'INFORMASI KEPATUHAN' : 'COMPLIANCE INFORMATION']);
    data.push([]);

    // Compliance checks
    data.push([isIndonesian ? 'Kepatuhan FIFO/FEFO' : 'FIFO/FEFO Compliance', isIndonesian ? 'Ya' : 'Yes']);
    data.push([isIndonesian ? 'Validasi Tanggal Kedaluwarsa' : 'Expiry Date Validation', isIndonesian ? 'Ya' : 'Yes']);
    data.push([isIndonesian ? 'Pelacakan Batch' : 'Batch Traceability', isIndonesian ? 'Ya' : 'Yes']);
    data.push([isIndonesian ? 'Akurasi Biaya' : 'Cost Accuracy', isIndonesian ? 'Ya' : 'Yes']);
    data.push([]);

    // Warnings
    if (allocationResult.warnings && allocationResult.warnings.length > 0) {
      data.push([isIndonesian ? 'PERINGATAN' : 'WARNINGS']);
      allocationResult.warnings.forEach((warning: string) => {
        data.push([warning]);
      });
      data.push([]);
    }

    // Errors
    if (allocationResult.errors && allocationResult.errors.length > 0) {
      data.push([isIndonesian ? 'ERROR' : 'ERRORS']);
      allocationResult.errors.forEach((error: string) => {
        data.push([error]);
      });
      data.push([]);
    }

    return data;
  }

  // CSV Helper Methods
  private escapeCsvValue(value: string): string {
    if (value.includes(',') || value.includes('"') || value.includes('\n')) {
      return `"${value.replace(/"/g, '""')}"`;
    }
    return value;
  }

  // PDF Helper Methods
  private addPDFHeader(doc: any, pharmacyInfo: any, isIndonesian: boolean): void {
    if (pharmacyInfo) {
      doc.fontSize(12)
         .font('Helvetica-Bold')
         .text(pharmacyInfo.name, { align: 'left' })
         .fontSize(10)
         .font('Helvetica')
         .text(pharmacyInfo.address)
         .text(`${isIndonesian ? 'Nomor Izin' : 'License'}: ${pharmacyInfo.licenseNumber}`)
         .text(`${isIndonesian ? 'Apoteker Penanggung Jawab' : 'Pharmacist in Charge'}: ${pharmacyInfo.pharmacistName}`)
         .moveDown();
    }
  }

  private addPDFAllocationSummary(doc: any, allocationResult: any, isIndonesian: boolean): void {
    doc.fontSize(14)
       .font('Helvetica-Bold')
       .text(isIndonesian ? 'RINGKASAN ALOKASI' : 'ALLOCATION SUMMARY')
       .moveDown(0.5);

    const summaryData = [
      [isIndonesian ? 'Jumlah Diminta' : 'Requested Quantity', allocationResult.requestedQuantity.toString()],
      [isIndonesian ? 'Jumlah Dialokasikan' : 'Allocated Quantity', allocationResult.allocatedQuantity.toString()],
      [isIndonesian ? 'Metode' : 'Method', allocationResult.method],
      [isIndonesian ? 'Total Biaya' : 'Total Cost', `Rp ${allocationResult.totalCost.toLocaleString('id-ID')}`],
      [isIndonesian ? 'Status' : 'Status', allocationResult.success ? (isIndonesian ? 'Berhasil' : 'Success') : (isIndonesian ? 'Gagal' : 'Failed')]
    ];

    doc.fontSize(10).font('Helvetica');
    summaryData.forEach(([label, value]) => {
      doc.text(`${label}: ${value}`);
    });
    doc.moveDown();
  }

  private addPDFBatchDetails(doc: any, batches: any[], isIndonesian: boolean): void {
    doc.fontSize(14)
       .font('Helvetica-Bold')
       .text(isIndonesian ? 'DETAIL BATCH' : 'BATCH DETAILS')
       .moveDown(0.5);

    doc.fontSize(10).font('Helvetica');

    batches.forEach((batch, index) => {
      doc.text(`${isIndonesian ? 'Batch' : 'Batch'} ${index + 1}:`)
         .text(`  ${isIndonesian ? 'Nomor Batch' : 'Batch Number'}: ${batch.batchNumber || 'N/A'}`)
         .text(`  ${isIndonesian ? 'Dialokasikan' : 'Allocated'}: ${batch.allocatedQuantity}`)
         .text(`  ${isIndonesian ? 'Tersedia' : 'Available'}: ${batch.availableQuantity}`)
         .text(`  ${isIndonesian ? 'Harga Satuan' : 'Unit Price'}: Rp ${batch.costPrice.toLocaleString('id-ID')}`);

      if (batch.expiryDate) {
        doc.text(`  ${isIndonesian ? 'Tanggal Kedaluwarsa' : 'Expiry Date'}: ${new Date(batch.expiryDate).toLocaleDateString(isIndonesian ? 'id-ID' : 'en-US')}`);
      }

      if (batch.location) {
        doc.text(`  ${isIndonesian ? 'Lokasi' : 'Location'}: ${batch.location}`);
      }

      doc.moveDown(0.5);
    });
  }

  private addPDFWarningsAndErrors(doc: any, allocationResult: any, isIndonesian: boolean): void {
    if (allocationResult.warnings && allocationResult.warnings.length > 0) {
      doc.fontSize(12)
         .font('Helvetica-Bold')
         .text(isIndonesian ? 'PERINGATAN' : 'WARNINGS')
         .moveDown(0.5);

      doc.fontSize(10).font('Helvetica');
      allocationResult.warnings.forEach((warning: string) => {
        doc.text(`• ${warning}`);
      });
      doc.moveDown();
    }

    if (allocationResult.errors && allocationResult.errors.length > 0) {
      doc.fontSize(12)
         .font('Helvetica-Bold')
         .text(isIndonesian ? 'ERROR' : 'ERRORS')
         .moveDown(0.5);

      doc.fontSize(10).font('Helvetica');
      allocationResult.errors.forEach((error: string) => {
        doc.text(`• ${error}`);
      });
      doc.moveDown();
    }
  }

  private addPDFFooter(doc: any, userInfo: any, isIndonesian: boolean): void {
    if (userInfo) {
      doc.fontSize(10)
         .font('Helvetica')
         .text(`${isIndonesian ? 'Dibuat oleh' : 'Generated by'}: ${userInfo.name} (${userInfo.role})`)
         .text(`${isIndonesian ? 'Waktu Pembuatan' : 'Generation Time'}: ${new Date().toLocaleString(isIndonesian ? 'id-ID' : 'en-US')}`);
    }
  }

  async cleanupExpiredReports(): Promise<void> {
    // Clean up expired report files
    // This should be called periodically by a cron job
    const files = fs.readdirSync(this.reportsDir);
    const now = new Date();

    for (const file of files) {
      const filePath = path.join(this.reportsDir, file);
      const stats = fs.statSync(filePath);
      const fileAge = now.getTime() - stats.mtime.getTime();
      const oneHour = 60 * 60 * 1000;

      if (fileAge > oneHour) {
        fs.unlinkSync(filePath);
        this.logger.info(`Cleaned up expired report: ${file}`);
      }
    }
  }
}
