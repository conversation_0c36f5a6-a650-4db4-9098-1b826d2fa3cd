import { Injectable, NotFoundException, BadRequestException, ConflictException, ForbiddenException } from '@nestjs/common';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { PrismaService } from '../prisma/prisma.service';
import { CreateInventoryItemDto } from './dto/create-inventory-item.dto';
import { UpdateInventoryItemDto } from './dto/update-inventory-item.dto';
import { InventoryQueryDto } from './dto/inventory-query.dto';
import { StockAdjustmentDto } from './dto/stock-adjustment.dto';
import { ActivateInventoryItemDto } from './dto/activate-inventory-item.dto';
import { DeactivateInventoryItemDto } from './dto/deactivate-inventory-item.dto';
import { StockAllocationDto, AllocationMethod } from './dto/stock-allocation.dto';
import { Prisma, StockMovementType, InventoryItem, PrismaClient } from '@prisma/client';
import { Decimal, DefaultArgs } from '@prisma/client/runtime/library';
import { ReferenceType } from './dto/reference-types.enum';

@Injectable()
export class InventoryService {
  constructor(
    private prisma: PrismaService,
    @InjectPinoLogger(InventoryService.name)
    private readonly logger: PinoLogger,
  ) { }

  private getInventoryItemInclude(): Prisma.InventoryItemInclude {
    return {
      product: {
        include: {
          baseUnit: true,
          unitHierarchies: {
            include: { unit: true },
            where: { isActive: true },
            orderBy: { level: 'asc' as const }
          }
        }
      },
      unit: true,
      supplier: true,
      stockMovements: {
        take: 10,
        orderBy: { createdAt: 'desc' as const },
      },
    };
  }

  async create(createInventoryItemDto: CreateInventoryItemDto, userId: string) {
    // Comprehensive validation
    await this.validateCreateInventoryItem(createInventoryItemDto);

    // Additional business logic validations
    await this.validateBusinessRules(createInventoryItemDto);

    // Create inventory item with transaction
    const result = await this.prisma.$transaction(async (prisma) => {
      const inventoryItem = await prisma.inventoryItem.create({
        data: {
          ...createInventoryItemDto,
          expiryDate: createInventoryItemDto.expiryDate
            ? new Date(createInventoryItemDto.expiryDate)
            : null,
          receivedDate: createInventoryItemDto.receivedDate
            ? new Date(createInventoryItemDto.receivedDate)
            : new Date(),
          costPrice: new Decimal(createInventoryItemDto.costPrice),
          sellingPrice: createInventoryItemDto.sellingPrice
            ? new Decimal(createInventoryItemDto.sellingPrice)
            : null,
          createdBy: userId,
        },
        include: this.getInventoryItemInclude(),
      });

      // Create initial stock movement for the received quantity
      if (inventoryItem.quantityOnHand > 0) {
        await prisma.stockMovement.create({
          data: {
            inventoryItemId: inventoryItem.id,
            type: StockMovementType.IN,
            quantity: inventoryItem.quantityOnHand,
            unitPrice: inventoryItem.costPrice,
            referenceType: ReferenceType.INITIAL_STOCK,
            reason: 'Entri stok awal',
            notes: 'Item inventori dibuat dengan stok awal',
            createdBy: userId,
          },
        });
      }

      return inventoryItem;
    });

    // Convert Decimal fields to numbers for JSON serialization
    return this.serializeInventoryItem(result);
  }

  private serializeInventoryItem(item: any) {
    return {
      ...item,
      costPrice: Number(item.costPrice),
      sellingPrice: item.sellingPrice ? Number(item.sellingPrice) : null,
    };
  }

  private async validateCreateInventoryItem(dto: CreateInventoryItemDto) {
    // Validate supplier first if provided (to get proper 404 for invalid supplier)
    if (dto.supplierId) {
      const supplier = await this.prisma.supplier.findUnique({
        where: { id: dto.supplierId }
      });

      if (!supplier) {
         throw new NotFoundException('Supplier tidak ditemukan');
      }

      if (supplier.status !== 'ACTIVE') {
        throw new BadRequestException('Tidak dapat menggunakan supplier yang tidak aktif');
      }
    }

    // Validate product exists and is active
    const product = await this.prisma.product.findUnique({
      where: { id: dto.productId },
      include: {
        baseUnit: true,
        unitHierarchies: {
          include: { unit: true }
        }
      }
    });

    if (!product) {
      throw new NotFoundException('Produk tidak ditemukan');
    }

    if (!product.isActive) {
      throw new BadRequestException('Tidak dapat membuat inventori untuk produk yang tidak aktif');
    }

    // Validate unit exists and is compatible with product
    const unit = await this.prisma.productUnit.findUnique({
      where: { id: dto.unitId }
    });

    if (!unit) {
      throw new NotFoundException('Unit tidak ditemukan');
    }

    if (!unit.isActive) {
      throw new BadRequestException('Tidak dapat menggunakan unit yang tidak aktif');
    }

    // Check if unit is compatible with product (base unit or in hierarchy)
    const isBaseUnit = product.baseUnitId === dto.unitId;
    const isInHierarchy = product.unitHierarchies.some(h => h.unitId === dto.unitId);

    if (!isBaseUnit && !isInHierarchy) {
      throw new BadRequestException('Unit tidak kompatibel dengan produk ini');
    }
  }

  private async validateBusinessRules(dto: CreateInventoryItemDto) {
    // Check for duplicate batch numbers for the same product
    if (dto.batchNumber) {
      const existingBatch = await this.prisma.inventoryItem.findFirst({
        where: {
          productId: dto.productId,
          batchNumber: dto.batchNumber,
          isActive: true,
        },
      });

      if (existingBatch) {
        throw new ConflictException(
          `Nomor batch ${dto.batchNumber} sudah ada untuk produk ini`
        );
      }
    }

    // Validate expiry date is in the future
    if (dto.expiryDate) {
      const expiryDate = new Date(dto.expiryDate);
      const now = new Date();
      if (expiryDate <= now) {
        throw new BadRequestException('Tanggal kedaluwarsa harus di masa depan');
      }
    }

    // Validate received date is not in the future
    if (dto.receivedDate) {
      const receivedDate = new Date(dto.receivedDate);
      const now = new Date();
      if (receivedDate > now) {
        throw new BadRequestException('Received date cannot be in the future');
      }
    }

    // Validate selling price is greater than cost price if provided
    if (dto.sellingPrice && dto.sellingPrice <= dto.costPrice) {
      throw new BadRequestException('Selling price must be greater than cost price');
    }
  }

  async findAll(query: InventoryQueryDto) {
    const {
      page = 1,
      limit = 10,
      cursor,
      search,
      searchOperator = 'contains',
      productId,
      productIds,
      supplierId,
      supplierIds,
      location,
      isActive,
      lowStock,
      lowStockThreshold,
      expiringSoon,
      expiringSoonDays = 30,
      expired,
      batchNumber,
      productType,
      productCategory,
      quantityMin,
      quantityMax,
      costPriceMin,
      costPriceMax,
      sellingPriceMin,
      sellingPriceMax,
      expiryDateFrom,
      expiryDateTo,
      receivedDateFrom,
      receivedDateTo,
      createdDateFrom,
      createdDateTo,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      sortFields,
      includeAggregations,
      includeMovementStats,
      paginationType = 'offset',
    } = query;

    // Handle pagination
    const skip = paginationType === 'offset' ? (page - 1) * limit : undefined;
    const cursorCondition = cursor && paginationType === 'cursor'
      ? { id: { gt: cursor } }
      : undefined;

    const where: Prisma.InventoryItemWhereInput = {
      ...cursorCondition,
    };

    // Enhanced search functionality with operators
    if (search) {
      const searchCondition = this.buildSearchCondition(search, searchOperator);
      where.OR = [
        // Product search
        { product: { name: searchCondition } },
        { product: { code: searchCondition } },
        { product: { genericName: searchCondition } },
        { product: { manufacturer: searchCondition } },
        { product: { bpomNumber: searchCondition } },
        // Inventory specific search
        { batchNumber: searchCondition },
        { location: searchCondition },
        { notes: searchCondition },
        // Supplier search
        { supplier: { name: searchCondition } },
        { supplier: { code: searchCondition } },
      ];
    }

    // Product filtering
    if (productId) {
      where.productId = productId;
    }
    if (productIds && productIds.length > 0) {
      where.productId = { in: productIds };
    }
    if (productType) {
      where.product = {
        ...(where.product as any || {}),
        type: productType
      };
    }
    if (productCategory) {
      where.product = {
        ...(where.product as any || {}),
        category: productCategory
      };
    }

    // Supplier filtering
    if (supplierId) {
      where.supplierId = supplierId;
    }
    if (supplierIds && supplierIds.length > 0) {
      where.supplierId = { in: supplierIds };
    }

    // Basic filters
    if (location) {
      where.location = { contains: location, mode: 'insensitive' };
    }
    if (batchNumber) {
      where.batchNumber = { contains: batchNumber, mode: 'insensitive' };
    }
    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    // Quantity range filtering
    if (quantityMin !== undefined || quantityMax !== undefined) {
      where.quantityOnHand = {};
      if (quantityMin !== undefined) {
        where.quantityOnHand.gte = quantityMin;
      }
      if (quantityMax !== undefined) {
        where.quantityOnHand.lte = quantityMax;
      }
    }

    // Price range filtering
    if (costPriceMin !== undefined || costPriceMax !== undefined) {
      where.costPrice = {};
      if (costPriceMin !== undefined) {
        where.costPrice.gte = new Decimal(costPriceMin);
      }
      if (costPriceMax !== undefined) {
        where.costPrice.lte = new Decimal(costPriceMax);
      }
    }

    if (sellingPriceMin !== undefined || sellingPriceMax !== undefined) {
      where.sellingPrice = {};
      if (sellingPriceMin !== undefined) {
        where.sellingPrice.gte = new Decimal(sellingPriceMin);
      }
      if (sellingPriceMax !== undefined) {
        where.sellingPrice.lte = new Decimal(sellingPriceMax);
      }
    }

    // Date range filtering
    if (expiryDateFrom || expiryDateTo) {
      where.expiryDate = {};
      if (expiryDateFrom) {
        where.expiryDate.gte = new Date(expiryDateFrom);
      }
      if (expiryDateTo) {
        where.expiryDate.lte = new Date(expiryDateTo);
      }
    }

    if (receivedDateFrom || receivedDateTo) {
      where.receivedDate = {};
      if (receivedDateFrom) {
        where.receivedDate.gte = new Date(receivedDateFrom);
      }
      if (receivedDateTo) {
        where.receivedDate.lte = new Date(receivedDateTo);
      }
    }

    if (createdDateFrom || createdDateTo) {
      where.createdAt = {};
      if (createdDateFrom) {
        where.createdAt.gte = new Date(createdDateFrom);
      }
      if (createdDateTo) {
        where.createdAt.lte = new Date(createdDateTo);
      }
    }

    // Specific filters
    if (productId) {
      where.productId = productId;
    }

    if (supplierId) {
      where.supplierId = supplierId;
    }

    if (location) {
      where.location = { contains: location, mode: 'insensitive' };
    }

    if (batchNumber) {
      where.batchNumber = { contains: batchNumber, mode: 'insensitive' };
    }

    if (productType) {
      where.AND = [
        ...(Array.isArray(where.AND) ? where.AND : where.AND ? [where.AND] : []),
        { product: { type: productType } }
      ];
    }

    if (productCategory) {
      where.AND = [
        ...(Array.isArray(where.AND) ? where.AND : where.AND ? [where.AND] : []),
        { product: { category: productCategory } }
      ];
    }

    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    // Advanced stock level filtering
    if (lowStock) {
      const threshold = lowStockThreshold || 10;
      where.AND = [
        ...(Array.isArray(where.AND) ? where.AND : where.AND ? [where.AND] : []),
        {
          OR: [
            // Items with stock below threshold
            { quantityOnHand: { lt: threshold } },
            // Items with zero stock
            { quantityOnHand: { equals: 0 } }
          ]
        }
      ];
    }

    // Expiry date filtering
    const now = new Date();
    if (expiringSoon) {
      const daysAhead = expiringSoonDays || 30;
      const futureDate = new Date();
      futureDate.setDate(now.getDate() + daysAhead);

      where.AND = [
        ...(Array.isArray(where.AND) ? where.AND : where.AND ? [where.AND] : []),
        {
          expiryDate: {
            lte: futureDate,
            gte: now, // Not already expired
          }
        }
      ];
    }

    if (expired) {
      where.AND = [
        ...(Array.isArray(where.AND) ? where.AND : where.AND ? [where.AND] : []),
        {
          expiryDate: {
            lt: now,
          }
        }
      ];
    }

    // Handle special sorting cases
    if (sortBy === 'stockVelocity') {
      // Use raw SQL for stock velocity sorting
      return this.findAllWithStockVelocitySorting(query, where, skip, limit, sortOrder, paginationType, cursor, includeAggregations, includeMovementStats);
    }

    // Build order by clause for standard sorting
    const orderBy = this.buildOrderBy(sortBy, sortOrder, query.sortFields);

    // Prepare query options
    const queryOptions: any = {
      where,
      orderBy,
      include: this.getInventoryItemInclude(),
    };

    // Handle pagination
    if (paginationType === 'cursor' && cursor) {
      queryOptions.cursor = { id: cursor };
      queryOptions.skip = 1; // Skip the cursor item
      queryOptions.take = limit;
    } else {
      queryOptions.skip = skip;
      queryOptions.take = limit;
    }

    // Execute queries
    const promises: Promise<any>[] = [
      this.prisma.inventoryItem.findMany(queryOptions),
      this.prisma.inventoryItem.count({ where }),
    ];

    // Add aggregations if requested
    if (includeAggregations) {
      promises.push(
        this.prisma.inventoryItem.aggregate({
          where,
          _sum: { quantityOnHand: true, costPrice: true },
          _avg: { costPrice: true, quantityOnHand: true },
          _min: { costPrice: true, expiryDate: true },
          _max: { costPrice: true, expiryDate: true },
        })
      );
    }

    // Add movement stats if requested
    if (includeMovementStats) {
      const inventoryItemIds = await this.prisma.inventoryItem.findMany({
        where,
        select: { id: true },
      });

      promises.push(
        this.prisma.stockMovement.groupBy({
          by: ['type'],
          where: {
            inventoryItemId: { in: inventoryItemIds.map(item => item.id) },
            createdAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }, // Last 30 days
          },
          _sum: { quantity: true },
          _count: { id: true },
        })
      );
    }

    const results = await Promise.all(promises);
    const [items, total] = results;
    const aggregations = includeAggregations ? results[2] : null;
    const movementStats = includeMovementStats ? results[includeAggregations ? 3 : 2] : null;

    // Get allocation summaries for all items in a single query
    const allocationSummaries = await this.getAllocationSummariesForItems(items.map((item: any) => item.productId));

    // Serialize items with allocation data
    const serializedItems = items.map((item: any) => {
      const serializedItem = this.serializeInventoryItem(item);
      const allocationSummary = allocationSummaries[item.productId];

      return {
        ...serializedItem,
        allocationSummary: allocationSummary || {
          totalAllocated: 0,
          lastAllocation: null,
          recentAllocations: 0,
        },
      };
    });

    // Build response
    const response: any = {
      data: serializedItems,
      meta: {
        total,
        page: paginationType === 'offset' ? page : undefined,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: paginationType === 'offset'
          ? page < Math.ceil(total / limit)
          : serializedItems.length === limit,
        hasPreviousPage: paginationType === 'offset' ? page > 1 : !!cursor,
        nextCursor: paginationType === 'cursor' && serializedItems.length === limit
          ? serializedItems[serializedItems.length - 1].id
          : null,
        paginationType,
      },
    };

    // Add aggregations to response
    if (includeAggregations && aggregations) {
      response.aggregations = {
        totalQuantity: aggregations._sum.quantityOnHand || 0,
        totalValue: aggregations._sum.costPrice ? Number(aggregations._sum.costPrice) : 0,
        averagePrice: aggregations._avg.costPrice ? Number(aggregations._avg.costPrice) : 0,
        averageQuantity: aggregations._avg.quantityOnHand || 0,
        minPrice: aggregations._min.costPrice ? Number(aggregations._min.costPrice) : 0,
        maxPrice: aggregations._max.costPrice ? Number(aggregations._max.costPrice) : 0,
        earliestExpiry: aggregations._min.expiryDate,
        latestExpiry: aggregations._max.expiryDate,
      };
    }

    // Add movement stats to response
    if (includeMovementStats && movementStats) {
      response.movementStats = movementStats.map((stat: any) => ({
        type: stat.type,
        totalQuantity: stat._sum.quantity || 0,
        totalMovements: stat._count.id,
      }));
    }

    return response;
  }

  private buildSearchCondition(search: string, operator: string) {
    const mode = 'insensitive' as const;

    switch (operator) {
      case 'startsWith':
        return { startsWith: search, mode };
      case 'endsWith':
        return { endsWith: search, mode };
      case 'equals':
        return { equals: search, mode };
      case 'contains':
      default:
        return { contains: search, mode };
    }
  }

  /**
   * Enhanced findAll method with true stock velocity sorting using raw SQL
   * Calculates actual movement velocity based on stock movements in the last 30-90 days
   */
  private async findAllWithStockVelocitySorting(
    query: InventoryQueryDto,
    where: Prisma.InventoryItemWhereInput,
    skip: number | undefined,
    limit: number,
    sortOrder: 'asc' | 'desc',
    paginationType: 'offset' | 'cursor',
    cursor: string | undefined,
    includeAggregations: boolean | undefined,
    includeMovementStats: boolean | undefined
  ) {
    const { page = 1 } = query;

    // Calculate velocity period (default: 30 days)
    const velocityPeriodDays = 30;
    const velocityStartDate = new Date();
    velocityStartDate.setDate(velocityStartDate.getDate() - velocityPeriodDays);

    try {
      // Build WHERE clause for raw SQL
      const whereConditions = this.buildRawSqlWhereClause(where);
      const orderDirection = sortOrder.toUpperCase();

      // Raw SQL query to calculate stock velocity and sort by it
      const rawQuery = `
        WITH inventory_velocity AS (
          SELECT
            ii.id,
            ii."productId",
            ii."unitId",
            ii."batchNumber",
            ii."expiryDate",
            ii."quantityOnHand",
            ii."costPrice",
            ii."sellingPrice",
            ii."location",
            ii."receivedDate",
            ii."supplierId",
            ii."isActive",
            ii."notes",
            ii."createdAt",
            ii."updatedAt",
            ii."createdBy",
            ii."updatedBy",
            -- Calculate stock velocity as movements per day
            COALESCE(
              (
                SELECT
                  COUNT(sm.id)::DECIMAL / GREATEST(1, EXTRACT(EPOCH FROM (NOW() - $1::timestamp)) / 86400)
                FROM "stock_movements" sm
                WHERE sm."inventoryItemId" = ii.id
                  AND sm."createdAt" >= $1::timestamp
                  AND sm.type IN ('IN', 'OUT')
              ),
              0
            ) as velocity,
            -- Also calculate total quantity moved for tie-breaking
            COALESCE(
              (
                SELECT SUM(ABS(sm.quantity))
                FROM "stock_movements" sm
                WHERE sm."inventoryItemId" = ii.id
                  AND sm."createdAt" >= $1::timestamp
                  AND sm.type IN ('IN', 'OUT')
              ),
              0
            ) as total_quantity_moved
          FROM "inventory_items" ii
          ${whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''}
        )
        SELECT * FROM inventory_velocity
        ORDER BY
          velocity ${orderDirection},
          total_quantity_moved ${orderDirection},
          "createdAt" DESC
        ${paginationType === 'offset' ? `LIMIT $2 OFFSET $3` : `LIMIT $2`}
      `;

      // Prepare parameters - all parameters must be compatible with Prisma's raw query
      const params: any[] = [velocityStartDate];

      // Add limit and offset parameters
      if (paginationType === 'offset') {
        params.push(limit);
        params.push(skip || 0);
      } else {
        params.push(limit);
        // For cursor pagination, we'd need to add cursor handling in the WHERE clause
        // This is a simplified implementation
      }

      // Execute raw query
      const rawResults = await this.prisma.$queryRawUnsafe(rawQuery, ...params);

      // Get total count for pagination metadata
      const countQuery = `
        SELECT COUNT(*) as total
        FROM "inventory_items" ii
        ${whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''}
      `;
      const countResult = await this.prisma.$queryRawUnsafe(countQuery) as any[];
      const total = parseInt(countResult[0]?.total || '0');

      // Transform raw results to include relations
      const inventoryItemIds = (rawResults as any[]).map(item => item.id);

      if (inventoryItemIds.length === 0) {
        return {
          data: [],
          meta: {
            total: 0,
            page: paginationType === 'offset' ? page : undefined,
            limit,
            totalPages: 0,
            hasNextPage: false,
            hasPreviousPage: false,
            paginationType,
          },
        };
      }

      // Fetch full inventory items with relations
      const inventoryItemsWithRelations = await this.prisma.inventoryItem.findMany({
        where: { id: { in: inventoryItemIds } },
        include: this.getInventoryItemInclude(),
      });

      // Sort the results to match the velocity order from raw query
      const sortedItems = inventoryItemIds.map(id =>
        inventoryItemsWithRelations.find(item => item.id === id)
      ).filter(Boolean);

      // Serialize items
      const serializedItems = sortedItems.map(item => this.serializeInventoryItem(item));

      // Add velocity information to each item for debugging/display
      const itemsWithVelocity = serializedItems.map((item, index) => {
        const rawItem = (rawResults as any[])[index];
        return {
          ...item,
          _velocity: {
            movementsPerDay: Number(rawItem?.velocity || 0),
            totalQuantityMoved: Number(rawItem?.total_quantity_moved || 0),
            calculationPeriodDays: velocityPeriodDays,
          },
        };
      });

      // Build response with metadata
      const response: any = {
        data: itemsWithVelocity,
        meta: {
          total,
          page: paginationType === 'offset' ? page : undefined,
          limit,
          totalPages: Math.ceil(total / limit),
          hasNextPage: paginationType === 'offset'
            ? page < Math.ceil(total / limit)
            : serializedItems.length === limit,
          hasPreviousPage: paginationType === 'offset' ? page > 1 : !!cursor,
          paginationType,
          velocityCalculation: {
            periodDays: velocityPeriodDays,
            startDate: velocityStartDate.toISOString(),
            method: 'movements_per_day',
          },
        },
      };

      // Add aggregations and movement stats if requested
      if (includeAggregations || includeMovementStats) {
        const promises: Promise<any>[] = [];

        if (includeAggregations) {
          promises.push(
            this.prisma.inventoryItem.aggregate({
              where,
              _sum: { quantityOnHand: true, costPrice: true },
              _avg: { costPrice: true, quantityOnHand: true },
              _min: { costPrice: true, expiryDate: true },
              _max: { costPrice: true, expiryDate: true },
            })
          );
        }

        if (includeMovementStats) {
          promises.push(
            this.prisma.stockMovement.groupBy({
              by: ['type'],
              where: {
                inventoryItemId: { in: inventoryItemIds },
                createdAt: { gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
              },
              _sum: { quantity: true },
              _count: { id: true },
            })
          );
        }

        const results = await Promise.all(promises);
        let resultIndex = 0;

        if (includeAggregations) {
          const aggregations = results[resultIndex++];
          response.aggregations = {
            totalQuantity: aggregations._sum.quantityOnHand || 0,
            totalValue: aggregations._sum.costPrice ? Number(aggregations._sum.costPrice) : 0,
            averagePrice: aggregations._avg.costPrice ? Number(aggregations._avg.costPrice) : 0,
            averageQuantity: aggregations._avg.quantityOnHand || 0,
            minPrice: aggregations._min.costPrice ? Number(aggregations._min.costPrice) : 0,
            maxPrice: aggregations._max.costPrice ? Number(aggregations._max.costPrice) : 0,
            earliestExpiry: aggregations._min.expiryDate,
            latestExpiry: aggregations._max.expiryDate,
          };
        }

        if (includeMovementStats) {
          const movementStats = results[resultIndex++];
          response.movementStats = movementStats.map((stat: any) => ({
            type: stat.type,
            totalQuantity: stat._sum.quantity || 0,
            totalMovements: stat._count.id,
          }));
        }
      }

      return response;

    } catch (error) {
      // Fallback to regular sorting if raw SQL fails
      console.error('Stock velocity sorting failed, falling back to updatedAt sorting:', error);

      // Use regular findAll with updatedAt sorting as fallback
      const fallbackQuery = { ...query, sortBy: 'updatedAt' };

      // Call the regular findAll logic (extract the common logic)
      const orderBy = this.buildOrderBy('updatedAt', sortOrder);
      const queryOptions: any = {
        where,
        orderBy,
        include: this.getInventoryItemInclude(),
      };

      if (paginationType === 'cursor' && cursor) {
        queryOptions.cursor = { id: cursor };
        queryOptions.skip = 1;
        queryOptions.take = limit;
      } else {
        queryOptions.skip = skip;
        queryOptions.take = limit;
      }

      const [items, total] = await Promise.all([
        this.prisma.inventoryItem.findMany(queryOptions),
        this.prisma.inventoryItem.count({ where }),
      ]);

      const serializedItems = items.map((item: any) => this.serializeInventoryItem(item));

      return {
        data: serializedItems,
        meta: {
          total,
          page: paginationType === 'offset' ? query.page || 1 : undefined,
          limit,
          totalPages: Math.ceil(total / limit),
          hasNextPage: paginationType === 'offset'
            ? (query.page || 1) < Math.ceil(total / limit)
            : serializedItems.length === limit,
          hasPreviousPage: paginationType === 'offset' ? (query.page || 1) > 1 : !!cursor,
          paginationType,
          fallbackUsed: true,
          fallbackReason: 'Stock velocity calculation failed',
        },
      };
    }
  }

  /**
   * Helper method to build WHERE clause conditions for raw SQL
   * Simplified version to handle basic filtering for stock velocity sorting
   */
  private buildRawSqlWhereClause(where: Prisma.InventoryItemWhereInput): string[] {
    const conditions: string[] = [];

    try {
      // Handle basic field conditions
      if (where.isActive !== undefined) {
        conditions.push(`ii."isActive" = ${where.isActive}`);
      }

      // Handle productId - simplified to avoid complex type checking
      if (where.productId) {
        if (typeof where.productId === 'string') {
          conditions.push(`ii."productId" = '${where.productId}'`);
        } else if (where.productId && typeof where.productId === 'object' && 'in' in where.productId) {
          const productIds = where.productId.in;
          if (Array.isArray(productIds)) {
            const ids = productIds.map(id => `'${id}'`).join(',');
            conditions.push(`ii."productId" IN (${ids})`);
          }
        }
      }

      // Handle supplierId - simplified
      if (where.supplierId) {
        if (typeof where.supplierId === 'string') {
          conditions.push(`ii."supplierId" = '${where.supplierId}'`);
        } else if (where.supplierId && typeof where.supplierId === 'object' && 'in' in where.supplierId) {
          const supplierIds = where.supplierId.in;
          if (Array.isArray(supplierIds)) {
            const ids = supplierIds.map(id => `'${id}'`).join(',');
            conditions.push(`ii."supplierId" IN (${ids})`);
          }
        }
      }

      // Handle quantityOnHand - simplified
      if (where.quantityOnHand && typeof where.quantityOnHand === 'object') {
        const qtyFilter = where.quantityOnHand as any;
        if (qtyFilter.gte !== undefined) {
          conditions.push(`ii."quantityOnHand" >= ${qtyFilter.gte}`);
        }
        if (qtyFilter.lte !== undefined) {
          conditions.push(`ii."quantityOnHand" <= ${qtyFilter.lte}`);
        }
        if (qtyFilter.lt !== undefined) {
          conditions.push(`ii."quantityOnHand" < ${qtyFilter.lt}`);
        }
        if (qtyFilter.equals !== undefined) {
          conditions.push(`ii."quantityOnHand" = ${qtyFilter.equals}`);
        }
      }

      // Handle basic string fields
      if (where.location && typeof where.location === 'object') {
        const locationFilter = where.location as any;
        if (locationFilter.contains) {
          conditions.push(`ii."location" ILIKE '%${locationFilter.contains}%'`);
        }
      }

      if (where.batchNumber && typeof where.batchNumber === 'object') {
        const batchFilter = where.batchNumber as any;
        if (batchFilter.contains) {
          conditions.push(`ii."batchNumber" ILIKE '%${batchFilter.contains}%'`);
        }
      }

      // For complex conditions like OR/AND, we'll use a simplified approach
      // In a production system, you'd want more comprehensive handling

    } catch (error) {
      // If there's any issue building the WHERE clause, return empty conditions
      // The query will still work but without filtering
      console.warn('Error building raw SQL WHERE clause:', error);
      return [];
    }

    return conditions;
  }

  private buildOrderBy(
    sortBy: string,
    sortOrder: 'asc' | 'desc',
    sortFields?: string[]
  ): Prisma.InventoryItemOrderByWithRelationInput | Prisma.InventoryItemOrderByWithRelationInput[] {
    const orderDirection = sortOrder as Prisma.SortOrder;

    // Handle multi-field sorting
    if (sortFields && sortFields.length > 0) {
      return sortFields.map(field => this.getSingleOrderBy(field, orderDirection));
    }

    return this.getSingleOrderBy(sortBy, orderDirection);
  }

  private getSingleOrderBy(sortBy: string, orderDirection: Prisma.SortOrder): Prisma.InventoryItemOrderByWithRelationInput {
    switch (sortBy) {
      // Product-related sorting
      case 'productName':
        return { product: { name: orderDirection } };
      case 'productCode':
        return { product: { code: orderDirection } };
      case 'productType':
        return { product: { type: orderDirection } };
      case 'productCategory':
        return { product: { category: orderDirection } };
      case 'productManufacturer':
        return { product: { manufacturer: orderDirection } };

      // Supplier-related sorting
      case 'supplierName':
        return { supplier: { name: orderDirection } };
      case 'supplierCode':
        return { supplier: { code: orderDirection } };
      case 'supplierType':
        return { supplier: { type: orderDirection } };

      // Unit-related sorting
      case 'unitName':
        return { unit: { name: orderDirection } };
      case 'unitAbbreviation':
        return { unit: { abbreviation: orderDirection } };
      case 'unitType':
        return { unit: { type: orderDirection } };

      // Calculated fields
      case 'stockValue':
        // For stock value (costPrice * quantityOnHand), we'll sort by costPrice as approximation
        // In production, this would use raw SQL for accurate calculation
        return { costPrice: orderDirection };

      case 'totalValue':
        return { costPrice: orderDirection };

      case 'expiryProximity':
        // Sort by how close to expiry (nulls last for items without expiry)
        return { expiryDate: orderDirection };

      case 'stockVelocity':
        // Stock velocity requires complex calculation - handled separately in findAll
        // This case should not be reached as stockVelocity is handled via raw SQL
        throw new BadRequestException('Stock velocity sorting requires special handling. Use findAll method with stockVelocity sort.');

      case 'lastMovementDate':
        // Sort by the most recent stock movement
        return { updatedAt: orderDirection };

      // Standard fields
      case 'batchNumber':
        return { batchNumber: orderDirection };
      case 'location':
        return { location: orderDirection };
      case 'quantityOnHand':
        return { quantityOnHand: orderDirection };
      case 'costPrice':
        return { costPrice: orderDirection };
      case 'sellingPrice':
        return { sellingPrice: orderDirection };
      case 'expiryDate':
        return { expiryDate: orderDirection };
      case 'receivedDate':
        return { receivedDate: orderDirection };
      case 'createdAt':
        return { createdAt: orderDirection };
      case 'updatedAt':
        return { updatedAt: orderDirection };

      // Default fallback
      default:
        // Check if it's a valid field on the model
        const validFields = [
          'id', 'productId', 'unitId', 'batchNumber', 'expiryDate',
          'quantityOnHand', 'costPrice', 'sellingPrice', 'location',
          'receivedDate', 'supplierId', 'isActive', 'notes',
          'createdAt', 'updatedAt', 'createdBy', 'updatedBy'
        ];

        if (validFields.includes(sortBy)) {
          return { [sortBy]: orderDirection };
        }

        // Default to createdAt if invalid field
        return { createdAt: orderDirection };
    }
  }

  async findOne(id: string) {
    const inventoryItem = await this.prisma.inventoryItem.findUnique({
      where: { id },
      include: {
        product: {
          include: {
            baseUnit: true,
            unitHierarchies: {
              include: { unit: true }
            }
          }
        },
        unit: true,
        supplier: true,
        stockMovements: {
          orderBy: { createdAt: 'desc' },
        },
      },
    });

    if (!inventoryItem) {
      throw new NotFoundException('Item inventori tidak ditemukan');
    }

    return this.serializeInventoryItem(inventoryItem);
  }

  async update(id: string, updateInventoryItemDto: UpdateInventoryItemDto, userId: string) {
    const existingItem = await this.findOne(id);

    // Validate unit if being updated
    if (updateInventoryItemDto.unitId && updateInventoryItemDto.unitId !== existingItem.unitId) {
      const unit = await this.prisma.productUnit.findUnique({
        where: { id: updateInventoryItemDto.unitId }
      });

      if (!unit) {
        throw new NotFoundException('Unit tidak ditemukan');
      }
    }

    // Validate supplier if being updated
    if (updateInventoryItemDto.supplierId && updateInventoryItemDto.supplierId !== existingItem.supplierId) {
      const supplier = await this.prisma.supplier.findUnique({
        where: { id: updateInventoryItemDto.supplierId }
      });

      if (!supplier) {
        throw new NotFoundException('Supplier tidak ditemukan');
      }
    }

    // Prepare update data with proper type conversion
    const updateData: any = {
      ...updateInventoryItemDto,
      updatedBy: userId,
    };

    // Convert dates if provided
    if (updateInventoryItemDto.expiryDate) {
      updateData.expiryDate = new Date(updateInventoryItemDto.expiryDate);
    }
    if (updateInventoryItemDto.receivedDate) {
      updateData.receivedDate = new Date(updateInventoryItemDto.receivedDate);
    }

    // Convert prices to Decimal if provided
    if (updateInventoryItemDto.costPrice !== undefined) {
      updateData.costPrice = new Decimal(updateInventoryItemDto.costPrice);
    }
    if (updateInventoryItemDto.sellingPrice !== undefined) {
      updateData.sellingPrice = updateInventoryItemDto.sellingPrice
        ? new Decimal(updateInventoryItemDto.sellingPrice)
        : null;
    }

    const updatedItem = await this.prisma.inventoryItem.update({
      where: { id },
      data: updateData,
      include: this.getInventoryItemInclude(),
    });

    return this.serializeInventoryItem(updatedItem);
  }

  async adjustStock(id: string, stockAdjustmentDto: StockAdjustmentDto, userId: string) {
    const inventoryItem = await this.findOne(id);

    const { quantity, reason, notes } = stockAdjustmentDto;
    const newQuantity = inventoryItem.quantityOnHand + quantity;

    if (newQuantity < 0) {
      throw new BadRequestException('Stok tidak mencukupi untuk penyesuaian');
    }

    // Update inventory quantity
    const updatedItem = await this.prisma.inventoryItem.update({
      where: { id },
      data: {
        quantityOnHand: newQuantity,
        updatedBy: userId,
      },
      include: {
        product: {
          include: {
            baseUnit: true,
            unitHierarchies: {
              include: { unit: true }
            }
          }
        },
        unit: true,
        supplier: true,
      },
    });

    // Create stock movement record
    await this.prisma.stockMovement.create({
      data: {
        inventoryItemId: id,
        type: quantity > 0 ? StockMovementType.IN : StockMovementType.OUT,
        quantity: Math.abs(quantity),
        referenceType: ReferenceType.ADJUSTMENT,
        reason,
        notes,
        createdBy: userId,
      },
    });

    return updatedItem;
  }

  /**
   * Main allocation method that automatically selects inventory items based on FIFO/FEFO logic
   * @param productId - Product ID to allocate stock from
   * @param requestedQuantity - Amount of stock to allocate
   * @param method - Allocation method: 'FIFO' (First In First Out) or 'FEFO' (First Expired First Out)
   * @param options - Additional allocation options
   * @returns Detailed allocation results including selected inventory items and allocated quantities
   */
  async allocateStock(
    productId: string,
    requestedQuantity: number,
    method: 'FIFO' | 'FEFO',
    options?: {
      userId?: string;
      reason?: string;
      notes?: string;
      allowPartialAllocation?: boolean;
      nearExpiryWarningDays?: number;
      previewOnly?: boolean;
      referenceType?: ReferenceType;
      referenceId?: string;
      referenceNumber?: string;
    },
    tx?: Omit<PrismaClient<Prisma.PrismaClientOptions, never, DefaultArgs>, "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends">
  ) {
    const allocationMethod = method === 'FIFO' ? AllocationMethod.FIFO : AllocationMethod.FEFO;
    const {
      userId = 'system',
      reason = `Alokasi stok menggunakan metode ${method}`,
      notes,
      allowPartialAllocation = true,
      nearExpiryWarningDays = 30,
      previewOnly = false,
      referenceType,
      referenceId,
      referenceNumber
    } = options || {};

    // Convert to StockAllocationDto format
    const stockAllocationDto: StockAllocationDto = {
      productId,
      requestedQuantity,
      method: allocationMethod,
      previewOnly,
      allowPartialAllocation,
      nearExpiryWarningDays,
      reason,
      notes,
      referenceType,
      referenceId,
      referenceNumber
    };

    // Use the StockAllocationService for allocation
    const allocationResult = await this.getStockAllocationService().allocateStock(
      stockAllocationDto,
      userId,
      undefined,
      tx
    );

    return allocationResult;
  }

  /**
   * Check if sufficient stock exists before allocation
   * @param productId - Product ID to check stock for
   * @param requestedQuantity - Required quantity to check availability
   * @returns Object with availability status and details
   */
  async validateStockAvailability(productId: string, requestedQuantity: number) {
    // Validate product exists
    const product = await this.prisma.product.findUnique({
      where: { id: productId },
      select: { id: true, name: true, isActive: true }
    });

    if (!product) {
      throw new NotFoundException(`Produk dengan ID ${productId} tidak ditemukan`);
    }

    if (!product.isActive) {
      throw new BadRequestException(`Produk ${product.name} tidak aktif`);
    }

    // Get total available stock
    const totalAvailable = await this.getAvailableStock(productId, false);

    // Get available inventory items for detailed analysis
    const availableItems = await this.prisma.inventoryItem.findMany({
      where: {
        productId,
        isActive: true,
        quantityOnHand: { gt: 0 },
        OR: [
          { expiryDate: null },
          { expiryDate: { gt: new Date() } }
        ]
      },
      select: {
        id: true,
        batchNumber: true,
        quantityOnHand: true,
        expiryDate: true,
        receivedDate: true,
        location: true
      },
      orderBy: [
        { expiryDate: 'asc' },
        { receivedDate: 'asc' }
      ]
    });

    const isAvailable = totalAvailable >= requestedQuantity;
    const shortfall = isAvailable ? 0 : requestedQuantity - totalAvailable;

    return {
      isAvailable,
      totalAvailable,
      requestedQuantity,
      shortfall,
      batchCount: availableItems.length,
      batches: availableItems.map(item => ({
        inventoryItemId: item.id,
        batchNumber: item.batchNumber,
        quantityOnHand: item.quantityOnHand,
        expiryDate: item.expiryDate,
        receivedDate: item.receivedDate,
        location: item.location
      }))
    };
  }

  /**
   * Get total available stock for a product
   * @param productId - Product ID to get stock for
   * @param includeExpired - Whether to include expired items in the count
   * @returns Total available quantity
   */
  async getAvailableStock(productId: string, includeExpired: boolean = false): Promise<number> {
    const now = new Date();

    // Use raw SQL for accurate calculation of available stock
    const query = `
      SELECT SUM(("quantityOnHand" - "quantityAllocated")) as available
      FROM "inventory_items"
      WHERE "productId" = $1
        AND "isActive" = true
        ${!includeExpired ? 'AND ("expiryDate" IS NULL OR "expiryDate" > $2)' : ''}
    `;

    const params = includeExpired ? [productId] : [productId, now];
    const result = await this.prisma.$queryRawUnsafe(query, ...params) as Array<{ available: string }>;

    // Parse the result and return the available stock
    const available = result[0]?.available;
    if (available === null || available === undefined) {
      return 0;
    }
    const parsed = parseInt(available);
    return isNaN(parsed) ? 0 : parsed;
  }

  /**
   * Preview which batches would be selected without actually allocating
   * @param productId - Product ID to preview allocation for
   * @param requestedQuantity - Amount to preview allocation for
   * @param method - Allocation method: 'FIFO' or 'FEFO'
   * @returns Preview of allocation without making changes
   */
  async previewAllocation(
    productId: string,
    requestedQuantity: number,
    method: 'FIFO' | 'FEFO'
  ) {
    return await this.allocateStock(productId, requestedQuantity, method, {
      previewOnly: true,
      allowPartialAllocation: true
    });
  }

  /**
   * Legacy method for backward compatibility
   * Allocate stock using FIFO/FEFO method for product-level stock reduction
   * This method is useful for sales, transfers, or other operations that need
   * to reduce stock across multiple batches following pharmacy best practices
   */
  async allocateStockForProduct(
    productId: string,
    quantity: number,
    method: AllocationMethod = AllocationMethod.FEFO,
    userId: string,
    reason: string,
    notes?: string
  ) {
    return await this.allocateStock(productId, quantity, method, {
      userId,
      reason,
      notes,
      allowPartialAllocation: false
    });
  }

  /**
   * Deallocate stock by reducing quantityAllocated from inventory items
   * This is used for cancellations, refunds, or other operations that need to restore allocated stock
   */
  async deallocateStock(
    productId: string,
    quantity: number,
    options: {
      referenceType?: ReferenceType;
      referenceId?: string;
      referenceNumber?: string;
      reason: string;
      notes?: string;
      userId?: string;
    }
  ) {
    const { referenceType, referenceId, referenceNumber, reason, notes, userId = 'system' } = options;

    // Validate product exists
    const product = await this.prisma.product.findUnique({
      where: { id: productId },
      include: { baseUnit: true }
    });

    if (!product) {
      throw new NotFoundException(`Produk dengan ID ${productId} tidak ditemukan`);
    }

    // Get inventory items that have allocated stock, ordered by allocation date (FIFO for deallocation)
    const allocatedItems = await this.prisma.inventoryItem.findMany({
      where: {
        productId,
        isActive: true,
        quantityAllocated: { gt: 0 }
      },
      orderBy: [
        { updatedAt: 'asc' }, // Deallocate from oldest allocations first
        { createdAt: 'asc' }
      ],
      include: {
        product: { include: { baseUnit: true } },
        unit: true
      }
    });

    if (allocatedItems.length === 0) {
      throw new BadRequestException(`Tidak ada stok yang dialokasikan untuk produk ${product.name}`);
    }

    let remainingQuantity = quantity;
    const deallocations: Array<{ inventoryItemId: string; quantity: number; costPrice: number }> = [];

    // Calculate how much to deallocate from each item
    for (const item of allocatedItems) {
      if (remainingQuantity <= 0) break;

      const deallocateFromThisItem = Math.min(remainingQuantity, item.quantityAllocated);

      if (deallocateFromThisItem > 0) {
        deallocations.push({
          inventoryItemId: item.id,
          quantity: deallocateFromThisItem,
          costPrice: Number(item.costPrice)
        });
        remainingQuantity -= deallocateFromThisItem;
      }
    }

    if (remainingQuantity > 0) {
      throw new BadRequestException(
        `Tidak dapat mendealokasi ${quantity} unit. Hanya ${quantity - remainingQuantity} unit yang dialokasikan untuk produk ${product.name}`
      );
    }

    // Execute deallocation in a transaction
    await this.prisma.$transaction(async (tx) => {
      for (const deallocation of deallocations) {
        // Update only quantityAllocated (not quantityOnHand)
        await tx.inventoryItem.update({
          where: { id: deallocation.inventoryItemId },
          data: {
            quantityAllocated: { decrement: deallocation.quantity },
            updatedBy: userId
          }
        });

        // Create stock movement record (negative quantity to indicate deallocation)
        await tx.stockMovement.create({
          data: {
            inventoryItemId: deallocation.inventoryItemId,
            type: StockMovementType.ALLOCATION, // Use ALLOCATION type with negative quantity
            quantity: -deallocation.quantity, // Negative to indicate deallocation
            unitPrice: new Decimal(deallocation.costPrice),
            referenceType: referenceType as ReferenceType || ReferenceType.DEALLOCATION,
            referenceId,
            referenceNumber,
            reason,
            notes: notes || `Dealokasi ${deallocation.quantity} unit - ${reason}`,
            createdBy: userId
          }
        });
      }
    });

    return {
      success: true,
      productId,
      deallocatedQuantity: quantity,
      deallocations: deallocations.map(d => ({
        inventoryItemId: d.inventoryItemId,
        quantity: d.quantity
      }))
    };
  }

  async remove(id: string) {
    // Verify item exists before deletion
    await this.findOne(id);

    return this.prisma.inventoryItem.delete({
      where: { id },
    });
  }

  async activate(id: string, activateDto: ActivateInventoryItemDto, userId: string) {
    const inventoryItem = await this.findOne(id);

    if (inventoryItem.isActive) {
      throw new BadRequestException('Item inventori sudah aktif');
    }

    const updatedItem = await this.prisma.$transaction(async (prisma) => {
      const updated = await prisma.inventoryItem.update({
        where: { id },
        data: {
          isActive: true,
          updatedBy: userId,
        },
        include: this.getInventoryItemInclude(),
      });

      // Create stock movement record for activation
      await prisma.stockMovement.create({
        data: {
          inventoryItemId: id,
          type: StockMovementType.ADJUSTMENT,
          quantity: 0, // No quantity change, just status change
          referenceType: ReferenceType.ACTIVATION,
          reason: activateDto.reason || 'Item inventori diaktifkan',
          notes: activateDto.notes,
          createdBy: userId,
        },
      });

      return updated;
    });

    return updatedItem;
  }

  async deactivate(id: string, deactivateDto: DeactivateInventoryItemDto, userId: string) {
    const inventoryItem = await this.findOne(id);

    if (!inventoryItem.isActive) {
      throw new BadRequestException('Item inventori sudah tidak aktif');
    }

    const updatedItem = await this.prisma.$transaction(async (prisma) => {
      const updated = await prisma.inventoryItem.update({
        where: { id },
        data: {
          isActive: false,
          updatedBy: userId,
        },
        include: this.getInventoryItemInclude(),
      });

      // Create stock movement record for deactivation
      await prisma.stockMovement.create({
        data: {
          inventoryItemId: id,
          type: StockMovementType.ADJUSTMENT,
          quantity: 0, // No quantity change, just status change
          referenceType: ReferenceType.DEACTIVATION,
          reason: deactivateDto.reason || 'Item inventori dinonaktifkan',
          notes: deactivateDto.notes,
          createdBy: userId,
        },
      });

      return updated;
    });

    return updatedItem;
  }

  async getStats() {
    const now = new Date();
    const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const ninetyDaysAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);

    // Basic counts with parallel execution
    const [
      totalItems,
      activeItems,
      lowStockItems,
      expiredItems,
      expiringSoonItems,
      zeroStockItems,
      totalValue,
      averageCostPrice,
      totalQuantity,
      recentMovements,
      topProducts,
      supplierPerformance,
      categoryBreakdown,
      stockAging,
      turnoverAnalysis,
      expiryAlerts,
      stockVelocity,
      trendAnalysis
    ] = await Promise.all([
      // Basic counts
      this.prisma.inventoryItem.count(),
      this.prisma.inventoryItem.count({ where: { isActive: true } }),
      this.prisma.inventoryItem.count({
        where: { quantityOnHand: { lte: 10 }, isActive: true }
      }),
      this.prisma.inventoryItem.count({
        where: { expiryDate: { lte: now }, isActive: true }
      }),
      this.prisma.inventoryItem.count({
        where: {
          expiryDate: { gte: now, lte: thirtyDaysFromNow },
          isActive: true
        }
      }),
      this.prisma.inventoryItem.count({
        where: { quantityOnHand: 0, isActive: true }
      }),

      // Value calculations
      this.prisma.inventoryItem.aggregate({
        _sum: { costPrice: true },
        where: { isActive: true }
      }),
      this.prisma.inventoryItem.aggregate({
        _avg: { costPrice: true },
        where: { isActive: true }
      }),
      this.prisma.inventoryItem.aggregate({
        _sum: { quantityOnHand: true },
        where: { isActive: true }
      }),

      // Recent movements
      this.prisma.stockMovement.findMany({
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: {
          inventoryItem: {
            include: { product: true }
          }
        }
      }),

      // Top products by stock value
      this.getTopProductsByValue(),

      // Supplier performance metrics
      this.getSupplierPerformance(),

      // Category breakdown
      this.getCategoryBreakdown(),

      // Stock aging analysis
      this.getStockAging(),

      // Inventory turnover analysis
      this.getTurnoverAnalysis(ninetyDaysAgo),

      // Expiry alerts with detailed breakdown
      this.getExpiryAlerts(),

      // Stock movement velocity
      this.getStockVelocity(thirtyDaysAgo),

      // Trend analysis
      this.getTrendAnalysis(thirtyDaysAgo)
    ]);

    const inactiveItems = totalItems - activeItems;

    return {
      // Basic metrics
      totalItems,
      activeItems,
      inactiveItems,
      lowStockItems,
      expiredItems,
      expiringSoonItems,
      zeroStockItems,
      totalValue: totalValue._sum.costPrice ? Number(totalValue._sum.costPrice) : 0,
      averageCostPrice: averageCostPrice._avg.costPrice ? Number(averageCostPrice._avg.costPrice) : 0,
      totalQuantity: totalQuantity._sum.quantityOnHand || 0,

      // Advanced analytics
      topProducts,
      supplierPerformance,
      categoryBreakdown,
      stockAging,
      turnoverAnalysis,
      expiryAlerts,
      stockVelocity,
      trendAnalysis,
      recentMovements,

      // Calculated metrics
      averageStockValue: totalItems > 0 ? (totalValue._sum.costPrice ? Number(totalValue._sum.costPrice) / totalItems : 0) : 0,
      stockTurnoverRate: turnoverAnalysis.averageTurnoverRate,
      inventoryHealth: this.calculateInventoryHealth({
        totalItems,
        expiredItems,
        expiringSoonItems,
        lowStockItems,
        zeroStockItems
      })
    };
  }

  // Helper methods for enhanced analytics
  private async getTopProductsByValue() {
    const topProducts = await this.prisma.inventoryItem.groupBy({
      by: ['productId'],
      where: { isActive: true },
      _sum: {
        quantityOnHand: true,
        costPrice: true,
      },
      _count: {
        id: true,
      },
      orderBy: {
        _sum: {
          costPrice: 'desc',
        },
      },
      take: 10,
    });

    // Get product details
    const productIds = topProducts.map(item => item.productId);
    const products = await this.prisma.product.findMany({
      where: { id: { in: productIds } },
      select: { id: true, name: true, code: true, type: true, category: true },
    });

    return topProducts.map(item => {
      const product = products.find(p => p.id === item.productId);
      return {
        productId: item.productId,
        productName: product?.name || 'Unknown',
        productCode: product?.code || 'Unknown',
        productType: product?.type,
        productCategory: product?.category,
        totalQuantity: item._sum.quantityOnHand || 0,
        totalValue: item._sum.costPrice ? Number(item._sum.costPrice) : 0,
        itemCount: item._count.id,
      };
    });
  }

  private async getSupplierPerformance() {
    const supplierStats = await this.prisma.inventoryItem.groupBy({
      by: ['supplierId'],
      where: {
        isActive: true,
        supplierId: { not: null }
      },
      _sum: {
        quantityOnHand: true,
        costPrice: true,
      },
      _count: {
        id: true,
      },
      _avg: {
        costPrice: true,
      },
    });

    // Get supplier details
    const supplierIds = supplierStats.map(item => item.supplierId).filter(Boolean) as string[];
    const suppliers = await this.prisma.supplier.findMany({
      where: { id: { in: supplierIds } },
      select: { id: true, name: true, code: true, type: true },
    });

    return supplierStats.map(item => {
      const supplier = suppliers.find(s => s.id === item.supplierId);
      return {
        supplierId: item.supplierId,
        supplierName: supplier?.name || 'Unknown',
        supplierCode: supplier?.code || 'Unknown',
        supplierType: supplier?.type,
        totalItems: item._count.id,
        totalQuantity: item._sum.quantityOnHand || 0,
        totalValue: item._sum.costPrice ? Number(item._sum.costPrice) : 0,
        averagePrice: item._avg.costPrice ? Number(item._avg.costPrice) : 0,
      };
    }).sort((a, b) => b.totalValue - a.totalValue);
  }

  private async getCategoryBreakdown() {
    const categoryStats = await this.prisma.inventoryItem.groupBy({
      by: ['productId'],
      where: { isActive: true },
      _sum: {
        quantityOnHand: true,
        costPrice: true,
      },
      _count: {
        id: true,
      },
    });

    // Get product categories
    const productIds = categoryStats.map(item => item.productId);
    const products = await this.prisma.product.findMany({
      where: { id: { in: productIds } },
      select: { id: true, category: true, type: true },
    });

    // Group by category
    const categoryMap = new Map();
    categoryStats.forEach(item => {
      const product = products.find(p => p.id === item.productId);
      const category = product?.category || 'UNCATEGORIZED';
      const type = product?.type || 'UNKNOWN';

      if (!categoryMap.has(category)) {
        categoryMap.set(category, {
          category,
          totalItems: 0,
          totalQuantity: 0,
          totalValue: 0,
          types: new Map(),
        });
      }

      const categoryData = categoryMap.get(category);
      categoryData.totalItems += item._count.id;
      categoryData.totalQuantity += item._sum.quantityOnHand || 0;
      categoryData.totalValue += item._sum.costPrice ? Number(item._sum.costPrice) : 0;

      if (!categoryData.types.has(type)) {
        categoryData.types.set(type, { count: 0, value: 0 });
      }
      const typeData = categoryData.types.get(type);
      typeData.count += item._count.id;
      typeData.value += item._sum.costPrice ? Number(item._sum.costPrice) : 0;
    });

    return Array.from(categoryMap.values()).map(category => ({
      ...category,
      types: Array.from(category.types.entries()).map(([type, data]) => ({
        type,
        count: data.count,
        value: data.value,
      })),
    })).sort((a, b) => b.totalValue - a.totalValue);
  }

  private async getStockAging() {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000);
    const ninetyDaysAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);

    const [recent, medium, old, veryOld] = await Promise.all([
      this.prisma.inventoryItem.aggregate({
        _count: { id: true },
        _sum: { costPrice: true, quantityOnHand: true },
        where: {
          isActive: true,
          receivedDate: { gte: thirtyDaysAgo },
        },
      }),
      this.prisma.inventoryItem.aggregate({
        _count: { id: true },
        _sum: { costPrice: true, quantityOnHand: true },
        where: {
          isActive: true,
          receivedDate: { gte: sixtyDaysAgo, lt: thirtyDaysAgo },
        },
      }),
      this.prisma.inventoryItem.aggregate({
        _count: { id: true },
        _sum: { costPrice: true, quantityOnHand: true },
        where: {
          isActive: true,
          receivedDate: { gte: ninetyDaysAgo, lt: sixtyDaysAgo },
        },
      }),
      this.prisma.inventoryItem.aggregate({
        _count: { id: true },
        _sum: { costPrice: true, quantityOnHand: true },
        where: {
          isActive: true,
          receivedDate: { lt: ninetyDaysAgo },
        },
      }),
    ]);

    return {
      recent: {
        period: '0-30 days',
        count: recent._count.id,
        quantity: recent._sum.quantityOnHand || 0,
        value: recent._sum.costPrice ? Number(recent._sum.costPrice) : 0,
      },
      medium: {
        period: '31-60 days',
        count: medium._count.id,
        quantity: medium._sum.quantityOnHand || 0,
        value: medium._sum.costPrice ? Number(medium._sum.costPrice) : 0,
      },
      old: {
        period: '61-90 days',
        count: old._count.id,
        quantity: old._sum.quantityOnHand || 0,
        value: old._sum.costPrice ? Number(old._sum.costPrice) : 0,
      },
      veryOld: {
        period: '90+ days',
        count: veryOld._count.id,
        quantity: veryOld._sum.quantityOnHand || 0,
        value: veryOld._sum.costPrice ? Number(veryOld._sum.costPrice) : 0,
      },
    };
  }

  private async getTurnoverAnalysis(fromDate: Date) {
    // Get stock movements for turnover calculation
    const movements = await this.prisma.stockMovement.groupBy({
      by: ['inventoryItemId'],
      where: {
        createdAt: { gte: fromDate },
        type: { in: [StockMovementType.OUT, StockMovementType.ADJUSTMENT] },
      },
      _sum: {
        quantity: true,
      },
    });

    // Get average inventory levels
    const inventoryItems = await this.prisma.inventoryItem.findMany({
      where: { isActive: true },
      select: {
        id: true,
        quantityOnHand: true,
        costPrice: true,
        product: { select: { name: true, code: true } },
      },
    });

    const turnoverData = movements.map(movement => {
      const item = inventoryItems.find(i => i.id === movement.inventoryItemId);
      const soldQuantity = Math.abs(movement._sum.quantity || 0);
      const averageInventory = item?.quantityOnHand || 1;
      const turnoverRate = averageInventory > 0 ? soldQuantity / averageInventory : 0;

      return {
        inventoryItemId: movement.inventoryItemId,
        productName: item?.product.name || 'Unknown',
        productCode: item?.product.code || 'Unknown',
        soldQuantity,
        averageInventory,
        turnoverRate,
        currentValue: item?.costPrice ? Number(item.costPrice) : 0,
      };
    }).sort((a, b) => b.turnoverRate - a.turnoverRate);

    const averageTurnoverRate = turnoverData.length > 0
      ? turnoverData.reduce((sum, item) => sum + item.turnoverRate, 0) / turnoverData.length
      : 0;

    return {
      averageTurnoverRate,
      topPerformers: turnoverData.slice(0, 10),
      slowMovers: turnoverData.slice(-10).reverse(),
      totalItemsAnalyzed: turnoverData.length,
    };
  }

  private async getExpiryAlerts() {
    const now = new Date();
    const sevenDays = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    const fourteenDays = new Date(now.getTime() + 14 * 24 * 60 * 60 * 1000);
    const thirtyDays = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

    const [critical, warning, notice, expired] = await Promise.all([
      this.prisma.inventoryItem.findMany({
        where: {
          isActive: true,
          expiryDate: { gte: now, lte: sevenDays },
        },
        include: {
          product: { select: { name: true, code: true } },
          unit: { select: { name: true, abbreviation: true } },
        },
        orderBy: { expiryDate: 'asc' },
      }),
      this.prisma.inventoryItem.findMany({
        where: {
          isActive: true,
          expiryDate: { gt: sevenDays, lte: fourteenDays },
        },
        include: {
          product: { select: { name: true, code: true } },
          unit: { select: { name: true, abbreviation: true } },
        },
        orderBy: { expiryDate: 'asc' },
      }),
      this.prisma.inventoryItem.findMany({
        where: {
          isActive: true,
          expiryDate: { gt: fourteenDays, lte: thirtyDays },
        },
        include: {
          product: { select: { name: true, code: true } },
          unit: { select: { name: true, abbreviation: true } },
        },
        orderBy: { expiryDate: 'asc' },
      }),
      this.prisma.inventoryItem.findMany({
        where: {
          isActive: true,
          expiryDate: { lte: now },
        },
        include: {
          product: { select: { name: true, code: true } },
          unit: { select: { name: true, abbreviation: true } },
        },
        orderBy: { expiryDate: 'desc' },
        take: 20,
      }),
    ]);

    return {
      critical: {
        period: '1-7 days',
        count: critical.length,
        items: critical.map(item => ({
          id: item.id,
          productName: item.product.name,
          productCode: item.product.code,
          batchNumber: item.batchNumber,
          expiryDate: item.expiryDate,
          quantityOnHand: item.quantityOnHand,
          costPrice: Number(item.costPrice),
          daysUntilExpiry: Math.ceil((item.expiryDate!.getTime() - now.getTime()) / (24 * 60 * 60 * 1000)),
        })),
      },
      warning: {
        period: '8-14 days',
        count: warning.length,
        items: warning.slice(0, 10).map(item => ({
          id: item.id,
          productName: item.product.name,
          productCode: item.product.code,
          batchNumber: item.batchNumber,
          expiryDate: item.expiryDate,
          quantityOnHand: item.quantityOnHand,
          costPrice: Number(item.costPrice),
          daysUntilExpiry: Math.ceil((item.expiryDate!.getTime() - now.getTime()) / (24 * 60 * 60 * 1000)),
        })),
      },
      notice: {
        period: '15-30 days',
        count: notice.length,
        items: notice.slice(0, 5).map(item => ({
          id: item.id,
          productName: item.product.name,
          productCode: item.product.code,
          batchNumber: item.batchNumber,
          expiryDate: item.expiryDate,
          quantityOnHand: item.quantityOnHand,
          costPrice: Number(item.costPrice),
          daysUntilExpiry: Math.ceil((item.expiryDate!.getTime() - now.getTime()) / (24 * 60 * 60 * 1000)),
        })),
      },
      expired: {
        period: 'Already expired',
        count: expired.length,
        items: expired.map(item => ({
          id: item.id,
          productName: item.product.name,
          productCode: item.product.code,
          batchNumber: item.batchNumber,
          expiryDate: item.expiryDate,
          quantityOnHand: item.quantityOnHand,
          costPrice: Number(item.costPrice),
          daysOverdue: Math.ceil((now.getTime() - item.expiryDate!.getTime()) / (24 * 60 * 60 * 1000)),
        })),
      },
    };
  }

  private async getStockVelocity(fromDate: Date) {
    const movements = await this.prisma.stockMovement.groupBy({
      by: ['inventoryItemId', 'type'],
      where: {
        createdAt: { gte: fromDate },
      },
      _sum: {
        quantity: true,
      },
      _count: {
        id: true,
      },
    });

    // Group by inventory item
    const velocityMap = new Map();
    movements.forEach(movement => {
      if (!velocityMap.has(movement.inventoryItemId)) {
        velocityMap.set(movement.inventoryItemId, {
          inventoryItemId: movement.inventoryItemId,
          inbound: 0,
          outbound: 0,
          adjustments: 0,
          totalMovements: 0,
        });
      }

      const data = velocityMap.get(movement.inventoryItemId);
      const quantity = Math.abs(movement._sum.quantity || 0);

      if (movement.type === StockMovementType.IN) {
        data.inbound += quantity;
      } else if (movement.type === StockMovementType.OUT) {
        data.outbound += quantity;
      } else if (movement.type === StockMovementType.TRANSFER) {
        // For transfers, we need to check the quantity sign
        if ((movement._sum.quantity || 0) > 0) {
          data.inbound += quantity;
        } else {
          data.outbound += quantity;
        }
      } else {
        data.adjustments += quantity;
      }

      data.totalMovements += movement._count.id;
    });

    // Get product details
    const inventoryItemIds = Array.from(velocityMap.keys());
    const inventoryItems = await this.prisma.inventoryItem.findMany({
      where: { id: { in: inventoryItemIds } },
      include: {
        product: { select: { name: true, code: true } },
      },
    });

    const velocityData = Array.from(velocityMap.values()).map(data => {
      const item = inventoryItems.find(i => i.id === data.inventoryItemId);
      const netMovement = data.inbound - data.outbound;
      const velocity = data.totalMovements / 30; // movements per day

      return {
        ...data,
        productName: item?.product.name || 'Unknown',
        productCode: item?.product.code || 'Unknown',
        netMovement,
        velocity,
        currentStock: item?.quantityOnHand || 0,
      };
    }).sort((a, b) => b.velocity - a.velocity);

    return {
      fastMoving: velocityData.slice(0, 10),
      slowMoving: velocityData.slice(-10).reverse(),
      averageVelocity: velocityData.length > 0
        ? velocityData.reduce((sum, item) => sum + item.velocity, 0) / velocityData.length
        : 0,
    };
  }

  private async getTrendAnalysis(fromDate: Date) {
    const now = new Date();
    const midPoint = new Date((fromDate.getTime() + now.getTime()) / 2);

    const [firstHalf, secondHalf] = await Promise.all([
      this.prisma.stockMovement.aggregate({
        _count: { id: true },
        _sum: { quantity: true },
        where: {
          createdAt: { gte: fromDate, lt: midPoint },
        },
      }),
      this.prisma.stockMovement.aggregate({
        _count: { id: true },
        _sum: { quantity: true },
        where: {
          createdAt: { gte: midPoint, lte: now },
        },
      }),
    ]);

    const firstHalfMovements = firstHalf._count.id;
    const secondHalfMovements = secondHalf._count.id;
    const movementTrend = firstHalfMovements > 0
      ? ((secondHalfMovements - firstHalfMovements) / firstHalfMovements) * 100
      : 0;

    return {
      movementTrend: {
        percentage: movementTrend,
        direction: movementTrend > 0 ? 'increasing' : movementTrend < 0 ? 'decreasing' : 'stable',
        firstPeriodMovements: firstHalfMovements,
        secondPeriodMovements: secondHalfMovements,
      },
      totalMovements: firstHalfMovements + secondHalfMovements,
      averageMovementsPerDay: (firstHalfMovements + secondHalfMovements) / 30,
    };
  }

  private calculateInventoryHealth(metrics: {
    totalItems: number;
    expiredItems: number;
    expiringSoonItems: number;
    lowStockItems: number;
    zeroStockItems: number;
  }) {
    const { totalItems, expiredItems, expiringSoonItems, lowStockItems, zeroStockItems } = metrics;

    if (totalItems === 0) return { score: 0, status: 'unknown', issues: [] };

    const expiredPercentage = (expiredItems / totalItems) * 100;
    const expiringSoonPercentage = (expiringSoonItems / totalItems) * 100;
    const lowStockPercentage = (lowStockItems / totalItems) * 100;
    const zeroStockPercentage = (zeroStockItems / totalItems) * 100;

    let score = 100;
    const issues: string[] = [];

    // Deduct points for various issues
    if (expiredPercentage > 5) {
      score -= Math.min(30, expiredPercentage * 2);
      issues.push(`${expiredPercentage.toFixed(1)}% items expired`);
    }

    if (expiringSoonPercentage > 10) {
      score -= Math.min(20, expiringSoonPercentage);
      issues.push(`${expiringSoonPercentage.toFixed(1)}% items expiring soon`);
    }

    if (lowStockPercentage > 15) {
      score -= Math.min(25, lowStockPercentage);
      issues.push(`${lowStockPercentage.toFixed(1)}% items low stock`);
    }

    if (zeroStockPercentage > 5) {
      score -= Math.min(15, zeroStockPercentage * 2);
      issues.push(`${zeroStockPercentage.toFixed(1)}% items out of stock`);
    }

    score = Math.max(0, score);

    let status: string;
    if (score >= 80) status = 'excellent';
    else if (score >= 60) status = 'good';
    else if (score >= 40) status = 'fair';
    else if (score >= 20) status = 'poor';
    else status = 'critical';

    return { score: Math.round(score), status, issues };
  }

  async getAllocationHistory(
    page: number = 1,
    limit: number = 20,
    filters?: {
      productId?: string;
      userId?: string;
      method?: string;
      startDate?: Date;
      endDate?: Date;
    }
  ) {
    const skip = (page - 1) * limit;

    const where: any = {
      type: StockMovementType.ALLOCATION,
    };

    if (filters?.productId) {
      where.inventoryItem = {
        productId: filters.productId,
      };
    }

    if (filters?.userId) {
      where.createdBy = filters.userId;
    }

    if (filters?.method) {
      where.notes = {
        contains: filters.method,
      };
    }

    if (filters?.startDate || filters?.endDate) {
      where.movementDate = {};
      if (filters.startDate) {
        where.movementDate.gte = filters.startDate;
      }
      if (filters.endDate) {
        where.movementDate.lte = filters.endDate;
      }
    }

    const [movements, total] = await Promise.all([
      this.prisma.stockMovement.findMany({
        where,
        include: {
          inventoryItem: {
            include: {
              product: true,
              unit: true,
            },
          },
        },
        orderBy: {
          movementDate: 'desc',
        },
        skip,
        take: limit,
      }),
      this.prisma.stockMovement.count({ where }),
    ]);

    return {
      data: movements,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPreviousPage: page > 1,
      },
    };
  }

  async getProductAllocationSummary(productId: string) {
    // Get total allocated quantity for a product
    const allocations = await this.prisma.stockMovement.findMany({
      where: {
        type: StockMovementType.ALLOCATION,
        inventoryItem: {
          productId: productId,
        },
      },
      select: {
        quantity: true,
        movementDate: true,
      },
      orderBy: {
        movementDate: 'desc',
      },
      take: 1, // Get most recent allocation
    });

    const totalAllocated = allocations.reduce((sum, allocation) => sum + Math.abs(allocation.quantity), 0);
    const lastAllocation = allocations[0]?.movementDate;

    return {
      totalAllocated,
      lastAllocation,
      recentAllocations: allocations.length,
    };
  }

  private async getAllocationSummariesForItems(productIds: string[]): Promise<Record<string, any>> {
    if (productIds.length === 0) {
      return {};
    }

    // NEW LOGIC: Use quantityAllocated field directly from inventory_items table
    // This is more accurate than summing stock movements
    const rawQuery = `
      SELECT
        ii."productId",
        COALESCE(SUM(ii."quantityAllocated"), 0) as "totalAllocated",
        MAX(sm."movementDate") as "lastAllocation",
        COUNT(CASE WHEN sm.type = 'ALLOCATION' THEN sm.id END) as "recentAllocations"
      FROM "inventory_items" ii
      LEFT JOIN "stock_movements" sm ON ii.id = sm."inventoryItemId"
        AND sm.type = 'ALLOCATION'
      WHERE ii."productId" = ANY($1::text[])
        AND ii."isActive" = true
      GROUP BY ii."productId"
    `;

    try {
      const results = await this.prisma.$queryRawUnsafe(rawQuery, productIds) as any[];

      // Build the summary object
      const summaries: Record<string, any> = {};

      // Initialize all products with zero values
      productIds.forEach(productId => {
        summaries[productId] = {
          totalAllocated: 0,
          lastAllocation: null,
          recentAllocations: 0,
        };
      });

      // Populate with actual allocation data
      results.forEach(result => {
        summaries[result.productId] = {
          totalAllocated: parseInt(result.totalAllocated) || 0,
          lastAllocation: result.lastAllocation,
          recentAllocations: parseInt(result.recentAllocations) || 0,
        };
      });

      return summaries;
    } catch (error) {
      console.error('Error fetching allocation summaries:', error);

      // Return empty summaries as fallback
      const summaries: Record<string, any> = {};
      productIds.forEach(productId => {
        summaries[productId] = {
          totalAllocated: 0,
          lastAllocation: null,
          recentAllocations: 0,
        };
      });

      return summaries;
    }
  }

  async getStockMovements(inventoryItemId: string, page = 1, limit = 20) {
    const skip = (page - 1) * limit;

    const [movements, total] = await Promise.all([
      this.prisma.stockMovement.findMany({
        where: { inventoryItemId },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          inventoryItem: {
            include: {
              product: { select: { name: true, code: true } },
              unit: { select: { name: true, abbreviation: true } },
            }
          }
        },
      }),
      this.prisma.stockMovement.count({ where: { inventoryItemId } }),
    ]);

    return {
      data: movements,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPreviousPage: page > 1,
      },
    };
  }

  /**
   * Helper method to get the StockAllocationService
   * This avoids circular dependency issues
   */
  private getStockAllocationService() {
    const { StockAllocationService } = require('./services/stock-allocation.service');
    return new StockAllocationService(this.prisma);
  }
}
