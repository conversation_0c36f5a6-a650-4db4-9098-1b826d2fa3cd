import { PrismaClient } from '@prisma/client';
import { UnitConversionService } from '../common/services/unit-conversion.service';
import { PrismaService } from '../prisma/prisma.service';

const prisma = new PrismaClient();
const prismaService = new PrismaService();
const logger = {
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  trace: jest.fn(),
  fatal: jest.fn(),
} as any;
const unitConversionService = new UnitConversionService(prismaService, logger);

async function testUnitConversions() {
  console.log('🧪 Testing Indonesian Pharmacy Unit Conversions...\n');

  // Initialize PrismaService
  await prismaService.onModuleInit();

  try {
    // Get test products
    const paracetamol = await prisma.product.findFirst({ where: { code: 'MED-001' } });
    const gentamicin = await prisma.product.findFirst({ where: { code: 'MED-006' } });
    const vitaminC = await prisma.product.findFirst({ where: { code: 'MED-007' } });
    const jamuTolakAngin = await prisma.product.findFirst({ where: { code: 'MED-008' } });

    if (!paracetamol || !gentamicin || !vitaminC || !jamuTolakAngin) {
      throw new Error('Test products not found. Please run seed first.');
    }

    // Get units
    const tabletUnit = await prisma.productUnit.findFirst({ where: { name: 'Tablet' } });
    const stripUnit = await prisma.productUnit.findFirst({ where: { name: 'Strip' } });
    const boxUnit = await prisma.productUnit.findFirst({ where: { name: 'Box' } });
    const gramUnit = await prisma.productUnit.findFirst({ where: { name: 'Gram' } });
    const tubeUnit = await prisma.productUnit.findFirst({ where: { name: 'Tube' } });
    const mlUnit = await prisma.productUnit.findFirst({ where: { name: 'Mililiter' } });
    const sachetUnit = await prisma.productUnit.findFirst({ where: { name: 'Sachet' } });

    if (!tabletUnit || !stripUnit || !boxUnit || !gramUnit || !tubeUnit || !mlUnit || !sachetUnit) {
      throw new Error('Required units not found.');
    }

    console.log('=== Test 1: Paracetamol Tablet Conversions ===');
    
    // Test: 2 strips to tablets (should be 20 tablets)
    const result1 = await unitConversionService.convertUnits(
      paracetamol.id,
      stripUnit.id,
      tabletUnit.id,
      2
    );
    console.log(`2 strips → ${result1.convertedQuantity} tablets (expected: 20)`);

    // Test: 1 box to strips (should be 10 strips)
    const result2 = await unitConversionService.convertUnits(
      paracetamol.id,
      boxUnit.id,
      stripUnit.id,
      1
    );
    console.log(`1 box → ${result2.convertedQuantity} strips (expected: 10)`);

    // Test: 50 tablets to base unit
    const result3 = await unitConversionService.convertToBaseUnit(
      paracetamol.id,
      tabletUnit.id,
      50
    );
    console.log(`50 tablets → ${result3.convertedQuantity} base units (expected: 50)`);

    console.log('\n=== Test 2: Salep Gentamicin Weight Conversions ===');
    
    // Test: 2 tubes to grams (should be 10 grams)
    const result4 = await unitConversionService.convertUnits(
      gentamicin.id,
      tubeUnit.id,
      gramUnit.id,
      2
    );
    console.log(`2 tubes → ${result4.convertedQuantity} grams (expected: 10)`);

    // Test: 15 grams to tubes (should be 3 tubes)
    const result5 = await unitConversionService.convertUnits(
      gentamicin.id,
      gramUnit.id,
      tubeUnit.id,
      15
    );
    console.log(`15 grams → ${result5.convertedQuantity} tubes (expected: 3)`);

    console.log('\n=== Test 3: Vitamin C Effervescent Multi-level Conversions ===');
    
    // Test: 1 box to tablets (should be 60 tablets)
    const result6 = await unitConversionService.convertUnits(
      vitaminC.id,
      boxUnit.id,
      tabletUnit.id,
      1
    );
    console.log(`1 box → ${result6.convertedQuantity} tablets (expected: 60)`);

    // Test: 3 tubes to tablets (should be 30 tablets)
    const result7 = await unitConversionService.convertUnits(
      vitaminC.id,
      tubeUnit.id,
      tabletUnit.id,
      3
    );
    console.log(`3 tubes → ${result7.convertedQuantity} tablets (expected: 30)`);

    console.log('\n=== Test 4: Jamu Tolak Angin Liquid Conversions ===');
    
    // Test: 4 sachets to ml (should be 60 ml)
    const result8 = await unitConversionService.convertUnits(
      jamuTolakAngin.id,
      sachetUnit.id,
      mlUnit.id,
      4
    );
    console.log(`4 sachets → ${result8.convertedQuantity} ml (expected: 60)`);

    // Test: 1 box to sachets (should be 8 sachets)
    const result9 = await unitConversionService.convertUnits(
      jamuTolakAngin.id,
      boxUnit.id,
      sachetUnit.id,
      1
    );
    console.log(`1 box → ${result9.convertedQuantity} sachets (expected: 8)`);

    console.log('\n=== Test 5: Unit Hierarchy Validation ===');
    
    const validation1 = await unitConversionService.validateUnitHierarchy(paracetamol.id);
    console.log(`Paracetamol hierarchy valid: ${validation1.isValid}`);
    if (!validation1.isValid) {
      console.log('Errors:', validation1.errors);
    }

    const validation2 = await unitConversionService.validateUnitHierarchy(gentamicin.id);
    console.log(`Gentamicin hierarchy valid: ${validation2.isValid}`);
    if (!validation2.isValid) {
      console.log('Errors:', validation2.errors);
    }

    console.log('\n=== Test 6: Product Unit Hierarchy Display ===');
    
    const hierarchy = await unitConversionService.getProductUnitHierarchy(paracetamol.id);
    console.log('Paracetamol Unit Hierarchy:');
    hierarchy.forEach(node => {
      console.log(`- Level ${node.level}: ${node.unitName} (${node.unitAbbreviation}) - Factor: ${node.conversionFactor}`);
      node.children.forEach(child => {
        console.log(`  - Level ${child.level}: ${child.unitName} (${child.unitAbbreviation}) - Factor: ${child.conversionFactor}`);
      });
    });

    console.log('\n✅ All unit conversion tests completed successfully!');

  } catch (error) {
    console.error('❌ Unit conversion test failed:', error);
    throw error;
  }
}

// Run tests if called directly
if (require.main === module) {
  testUnitConversions()
    .catch((error) => {
      console.error(error);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { testUnitConversions };
