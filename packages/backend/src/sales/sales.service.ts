import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import { PrismaService } from '../prisma/prisma.service';
import { CreateSaleDto } from './dto/create-sale.dto';
import { UpdateSaleDto } from './dto/update-sale.dto';
import { SaleQueryDto } from './dto/sale-query.dto';
import { SaleNumberGeneratorService } from './sale-number-generator.service';
import { InventoryService } from '../inventory/inventory.service';
import { ProductsService } from '../products/products.service';
import { CustomerType, SaleStatus, StockMovementType } from '@prisma/client';
import { StockConsumptionService } from '../inventory/services/stock-consumption.service';
import { ReferenceType } from '../inventory/dto/reference-types.enum';
import { UnitConversionService } from '../common/services/unit-conversion.service';

@Injectable()
export class SalesService {
  constructor(
    private prisma: PrismaService,
    private saleNumberGeneratorService: SaleNumberGeneratorService,
    private inventoryService: InventoryService,
    private productsService: ProductsService,
    private stockConsumptionService: StockConsumptionService,
    private unitConversionService: UnitConversionService,
    @InjectPinoLogger(SalesService.name)
    private readonly logger: PinoLogger,
  ) { }

  async create(createSaleDto: CreateSaleDto) {
    // Ensure cashierId is provided
    if (!createSaleDto.cashierId) {
      this.logger.warn('Sale creation failed: missing cashier ID');
      throw new BadRequestException('Cashier ID is required');
    }

    // Store cashierId as a string to avoid type issues
    const cashierId = createSaleDto.cashierId;

    // Generate sale number if not provided
    const saleNumber = createSaleDto.saleNumber || await this.saleNumberGeneratorService.generateSaleNumber();

    // Validate sale number uniqueness if provided
    if (createSaleDto.saleNumber) {
      const isUnique = await this.saleNumberGeneratorService.validateSaleNumberUniqueness(
        createSaleDto.saleNumber,
      );
      if (!isUnique) {
        this.logger.warn({ saleNumber: createSaleDto.saleNumber }, 'Sale creation failed: sale number already exists');
        throw new BadRequestException('Nomor transaksi sudah digunakan');
      }
    }

    // Calculate item totals with item-level discounts
    let rawSubtotal = 0;
    let totalItemDiscounts = 0;

    for (const item of createSaleDto.items) {
      const itemTotal = item.quantity * item.unitPrice;
      rawSubtotal += itemTotal;

      // Calculate item-level discount
      if (item.discountType && item.discountValue) {
        let itemDiscountAmount = 0;

        if (item.discountType === 'PERCENTAGE') {
          const discountPercentage = Math.min(100, item.discountValue);
          itemDiscountAmount = (itemTotal * discountPercentage) / 100;
        } else if (item.discountType === 'FIXED_AMOUNT' || item.discountType === 'FIXED') {
          itemDiscountAmount = Math.min(itemTotal, item.discountValue);
        }

        totalItemDiscounts += itemDiscountAmount;
      }
    }

    // Apply item-level discounts to subtotal
    const subtotalAfterItemDiscounts = rawSubtotal - totalItemDiscounts;

    // Calculate sale-level discount
    const saleDiscountAmount = createSaleDto.discountValue
      ? createSaleDto.discountType === 'PERCENTAGE'
        ? (subtotalAfterItemDiscounts * createSaleDto.discountValue) / 100
        : Math.min(subtotalAfterItemDiscounts, createSaleDto.discountValue)
      : 0;

    // Final subtotal after all discounts
    const subtotal = subtotalAfterItemDiscounts - saleDiscountAmount;
    const discountAmount = totalItemDiscounts + saleDiscountAmount;

    const taxAmount = createSaleDto.taxAmount || 0;
    const totalAmount = subtotal + taxAmount;
    const changeAmount = createSaleDto.amountPaid - totalAmount;

    // Comprehensive stock validation with unit conversion support
    await this.validateStockAvailability(createSaleDto.items);

    // Validate payment amount
    if (changeAmount < 0) {
      this.logger.warn({
        saleNumber,
        totalAmount,
        amountPaid: createSaleDto.amountPaid,
        shortfall: Math.abs(changeAmount)
      }, 'Sale creation failed: insufficient payment amount');
      throw new BadRequestException('Jumlah pembayaran tidak mencukupi');
    }

    // Execute transaction to ensure data consistency
    return this.prisma.$transaction(async (tx) => {
      // Create the sale
      const sale = await tx.sale.create({
        data: {
          saleNumber,
          customerId: createSaleDto.customerId || null,
          cashierId: cashierId,
          status: SaleStatus.COMPLETED, // Direct sales are created as COMPLETED
          saleDate: createSaleDto.saleDate ? new Date(createSaleDto.saleDate) : new Date(),
          customerName: createSaleDto.customerName,
          customerPhone: createSaleDto.customerPhone,
          subtotal,
          discountType: createSaleDto.discountType,
          discountValue: createSaleDto.discountValue,
          discountAmount,
          taxAmount,
          totalAmount,
          paymentMethod: createSaleDto.paymentMethod,
          amountPaid: createSaleDto.amountPaid,
          changeAmount,
          notes: createSaleDto.notes,
        },
      });

      // Create sale items first
      for (const item of createSaleDto.items) {
        // Calculate item discount
        const itemTotal = item.quantity * item.unitPrice;
        let itemDiscountAmount = 0;

        if (item.discountType && item.discountValue) {
          if (item.discountType === 'PERCENTAGE') {
            const discountPercentage = Math.min(100, item.discountValue);
            itemDiscountAmount = Math.round((itemTotal * discountPercentage) / 100);
          } else if (item.discountType === 'FIXED_AMOUNT' || item.discountType === 'FIXED') {
            itemDiscountAmount = Math.min(itemTotal, item.discountValue);
          }
        }

        await tx.saleItem.create({
          data: {
            saleId: sale.id,
            productId: item.productId,
            unitId: item.unitId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            totalPrice: itemTotal,
            discountType: item.discountType,
            discountValue: item.discountValue,
            discountAmount: itemDiscountAmount,
            notes: item.notes,
          },
        });
      }

      // Group items by product and allocate stock per product
      const productGroups = new Map<string, any[]>();
      for (const item of createSaleDto.items) {
        if (!productGroups.has(item.productId)) {
          productGroups.set(item.productId, []);
        }
        productGroups.get(item.productId)!.push(item);
      }

      // Allocate stock for each product group
      for (const [productId, productItems] of productGroups) {
        try {
          const product = await this.productsService.findOne(productId);
          const method = product.type === 'MEDICINE' ? 'FEFO' : 'FIFO';

          // Calculate total demand in base units for this product
          let totalDemandInBaseUnits = 0;
          for (const item of productItems) {
            const conversionResult = await this.unitConversionService.convertToBaseUnit(
              item.productId,
              item.unitId,
              item.quantity
            );

            if (!conversionResult.success) {
              throw new BadRequestException(
                `Gagal mengkonversi unit untuk produk ${product.name}: ${conversionResult.error}`
              );
            }

            totalDemandInBaseUnits += conversionResult.convertedQuantity!;
          }

          // Round up to the nearest integer for allocation (pharmacy practice)
          // This ensures we allocate enough stock to cover fractional units
          const roundedDemandInBaseUnits = Math.ceil(totalDemandInBaseUnits);

          // Check if there's already allocated stock for this product
          const inventoryItems = await tx.inventoryItem.findMany({
            where: {
              productId: productId,
              quantityAllocated: { gt: 0 },
              isActive: true
            },
            select: {
              id: true,
              quantityAllocated: true
            }
          });

          // Calculate total pre-allocated stock
          const totalAllocated = inventoryItems.reduce((sum, item) => sum + item.quantityAllocated, 0);

          // Only allocate the difference if pre-allocated stock is not enough
          const quantityToAllocate = Math.max(0, roundedDemandInBaseUnits - totalAllocated);

          if (quantityToAllocate > 0) {
            const allocation = await this.inventoryService.allocateStock(
              productId,
              quantityToAllocate,
              method,
              {
                allowPartialAllocation: false,
                userId: cashierId,
                reason: `Penjualan #${saleNumber}`,
                notes: `Pelanggan: ${createSaleDto.customerName || 'Walk-in'}`,
                referenceType: ReferenceType.SALE,
                referenceId: sale.id,
                referenceNumber: saleNumber
              },
            );

            if (!allocation.success) {
              const errorDetails = allocation.errors.length > 0 ? allocation.errors.join(', ') : 'Unknown allocation error';
              throw new BadRequestException(`${errorDetails}`);
            }
          }
        } catch (error) {
          // Check if it's a NotFoundException from the products service
          if (error.status === 404) {
            throw new NotFoundException(`Produk tidak ditemukan: ${error.message}`);
          }

          // For insufficient stock, throw a BadRequestException
          if (error.message && error.message.includes('Stok tidak mencukupi')) {
            throw new BadRequestException(`Stok tidak mencukupi: ${error.message}`);
          }

          // For other errors
          throw new BadRequestException(`Error saat mengalokasi stok: ${error.message}`);
        }
      }

      // Immediately consume the allocated stock to ensure data consistency
      // This ensures that stock is both allocated and consumed in a single transaction
      await this.consumeSaleStock(
        sale.id,
        cashierId,
        `Stok berhasil dialokasikan pada saat penjualan #${saleNumber}`,
        tx
      );

      // Convert Decimal fields to numbers for API response
      return {
        ...sale,
        subtotal: Number(sale.subtotal),
        discountAmount: Number(sale.discountAmount),
        taxAmount: Number(sale.taxAmount),
        totalAmount: Number(sale.totalAmount),
        amountPaid: Number(sale.amountPaid),
        changeAmount: Number(sale.changeAmount),
        discountValue: sale.discountValue ? Number(sale.discountValue) : null,
      };
    });
  }

  async findAll(query: SaleQueryDto) {
    const { page = 1, limit = 10, search, status, paymentMethod, customerId, cashierId, startDate, endDate, sortBy = 'createdAt', sortOrder = 'desc' } = query;
    const skip = (page - 1) * limit;

    const where: any = {};

    if (search) {
      where.OR = [
        { saleNumber: { contains: search, mode: 'insensitive' } },
        { customerName: { contains: search, mode: 'insensitive' } },
        { customerPhone: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (status) {
      where.status = status;
    }

    if (paymentMethod) {
      where.paymentMethod = paymentMethod;
    }

    if (customerId) {
      where.customerId = customerId;
    }

    if (cashierId) {
      where.cashierId = cashierId;
    }

    if (startDate || endDate) {
      where.saleDate = {};
      if (startDate) {
        where.saleDate.gte = new Date(startDate);
      }
      if (endDate) {
        where.saleDate.lte = new Date(endDate);
      }
    }

    try {
      const startTime = Date.now();
      const [sales, total] = await Promise.all([
        this.prisma.sale.findMany({
          where,
          skip,
          take: limit,
          orderBy: {
            [sortBy as string]: sortOrder,
          },
          include: {
            customer: {
              select: {
                id: true,
                code: true,
                fullName: true,
                phoneNumber: true,
                type: true,
              },
            },
            cashier: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            saleItems: {
              include: {
                product: {
                  select: {
                    id: true,
                    code: true,
                    name: true,
                    type: true,
                  },
                },
                unit: {
                  select: {
                    id: true,
                    name: true,
                    abbreviation: true,
                  },
                },
              },
            },
          },
        }),
        this.prisma.sale.count({ where }),
      ]);
      const queryTime = Date.now() - startTime;

      const result = {
        data: sales,
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
          hasNextPage: page < Math.ceil(total / limit),
          hasPreviousPage: page > 1,
        },
      };

      this.logger.info({
        total,
        returned: sales.length,
        page,
        limit,
        queryTimeMs: queryTime,
        hasFilters: !!(search || status || paymentMethod || customerId || cashierId || startDate || endDate)
      }, 'Sales retrieved successfully');

      this.logger.trace('Exiting findAll sales method');
      return result;
    } catch (error) {
      this.logger.error({ err: error, query }, 'Failed to retrieve sales');
      throw error;
    }
  }

  async findOne(id: string) {
    try {
      const sale = await this.prisma.sale.findUnique({
        where: { id },
        include: {
          customer: {
            select: {
              id: true,
              code: true,
              fullName: true,
              phoneNumber: true,
              email: true,
              type: true,
            },
          },
          cashier: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          saleItems: {
            include: {
              product: {
                select: {
                  id: true,
                  code: true,
                  name: true,
                  type: true,
                  manufacturer: true,
                },
              },
              unit: {
                select: {
                  id: true,
                  name: true,
                  abbreviation: true,
                },
              },
            },
          },
        },
      });

      if (!sale) {
        this.logger.warn({ saleId: id }, 'Sale not found');
        throw new NotFoundException('Transaksi tidak ditemukan');
      }

      // Convert Decimal fields to numbers for API response
      return {
        ...sale,
        subtotal: Number(sale.subtotal),
        discountValue: sale.discountValue ? Number(sale.discountValue) : null,
        discountAmount: Number(sale.discountAmount),
        taxAmount: Number(sale.taxAmount),
        totalAmount: Number(sale.totalAmount),
        amountPaid: Number(sale.amountPaid),
        changeAmount: Number(sale.changeAmount),
        saleItems: sale.saleItems.map(item => ({
          ...item,
          unitPrice: Number(item.unitPrice),
          totalPrice: Number(item.totalPrice),
          discountValue: item.discountValue ? Number(item.discountValue) : null,
          discountAmount: Number(item.discountAmount),
        })),
      };
    } catch (error) {
      if (!(error instanceof NotFoundException)) {
        this.logger.error({ err: error, saleId: id }, 'Failed to retrieve sale');
      }
      throw error;
    }
  }

  async getStats() {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    try {
      const startTime = Date.now();
      const [totalSales, todaySales, completedSales, totalRevenue, todayRevenue] = await Promise.all([
        this.prisma.sale.count(),
        this.prisma.sale.count({
          where: {
            saleDate: {
              gte: startOfDay,
              lt: endOfDay,
            },
          },
        }),
        this.prisma.sale.count({ where: { status: 'COMPLETED' } }),
        this.prisma.sale.aggregate({
          _sum: {
            totalAmount: true,
          },
          where: {
            status: 'COMPLETED',
          },
        }),
        this.prisma.sale.aggregate({
          _sum: {
            totalAmount: true,
          },
          where: {
            status: 'COMPLETED',
            saleDate: {
              gte: startOfDay,
              lt: endOfDay,
            },
          },
        }),
      ]);
      const queryTime = Date.now() - startTime;

      const stats = {
        totalSales,
        todaySales,
        completedSales,
        totalRevenue: Number(totalRevenue._sum.totalAmount) || 0,
        todayRevenue: Number(todayRevenue._sum.totalAmount) || 0,
      };

      this.logger.info({
        totalSales,
        todaySales,
        completedSales,
        totalRevenue: stats.totalRevenue,
        todayRevenue: stats.todayRevenue,
        queryTimeMs: queryTime
      }, 'Sales statistics retrieved successfully');

      this.logger.trace('Exiting getStats method');
      return stats;
    } catch (error) {
      this.logger.error({ err: error }, 'Failed to retrieve sales statistics');
      throw error;
    }
  }

  // Enhanced Transaction Processing Methods

  /**
   * Create a draft sale without stock allocation
   * Useful for building transactions before final confirmation
   */
  async createDraft(createSaleDto: CreateSaleDto) {
    // Ensure cashierId is provided
    if (!createSaleDto.cashierId) {
      throw new BadRequestException('Cashier ID is required');
    }

    // Generate sale number if not provided
    if (!createSaleDto.saleNumber) {
      createSaleDto.saleNumber = await this.saleNumberGeneratorService.generateSaleNumber();
    } else {
      // Validate sale number uniqueness if provided
      const isUnique = await this.saleNumberGeneratorService.validateSaleNumberUniqueness(
        createSaleDto.saleNumber,
      );
      if (!isUnique) {
        throw new BadRequestException('Nomor transaksi sudah digunakan');
      }
    }

    // Validate items and calculate totals
    let subtotal = 0;
    const processedItems: Array<{
      productId: string;
      unitId: string;
      quantity: number;
      unitPrice: number;
      totalPrice: number;
      discountType?: string;
      discountValue?: number;
      discountAmount: number;
      notes?: string;
    }> = [];

    for (const item of createSaleDto.items) {
      // Validate product exists
      const product = await this.productsService.findOne(item.productId);

      // Get product unit hierarchy to determine conversion factor
      const unitHierarchy = product.unitHierarchies?.find(h => h.unitId === item.unitId);

      // If we have a unit hierarchy, check stock (warn only for drafts)
      let availableWholeUnits = 0;
      if (unitHierarchy) {
        // Calculate base unit quantity needed
        const conversionFactor = Number(unitHierarchy.conversionFactor);

        // Check stock availability before processing
        const availableStock = await this.inventoryService.getAvailableStock(item.productId, false);

        // Calculate how many whole units are available
        availableWholeUnits = Math.floor(availableStock / conversionFactor);

        if (availableWholeUnits < item.quantity) {
          // For draft sales, we just log a warning but allow creation
          console.warn(`Warning: Insufficient stock for ${product.name}. Available: ${availableWholeUnits} ${unitHierarchy.unit?.name || 'unit'}, Requested: ${item.quantity}`);
        }
      } else {
        // Fallback to simple validation when unit hierarchy is missing (for tests)
        const availableStock = await this.inventoryService.getAvailableStock(item.productId, false);
        if (availableStock < item.quantity) {
          // For draft sales, we just log a warning but allow creation
          console.warn(`Warning: Insufficient stock for ${product.name}. Available: ${availableStock}, Requested: ${item.quantity}`);
        }
        availableWholeUnits = Math.floor(availableStock);
      }

      // Calculate item total
      const itemTotal = item.quantity * item.unitPrice;

      // Calculate item discount
      let itemDiscountAmount = 0;
      if (item.discountType && item.discountValue) {
        if (item.discountType === 'PERCENTAGE') {
          // For percentage discounts, round to nearest whole number
          // IMPORTANT: Prevent negative prices by capping percentage discounts at 100%
          // This validation is critical for financial integrity and is tested in the
          // regression test: "should prevent negative prices by capping discounts"
          const discountPercentage = Math.min(100, item.discountValue);
          itemDiscountAmount = Math.round((itemTotal * discountPercentage) / 100);
        } else if (item.discountType === 'FIXED_AMOUNT' || item.discountType === 'FIXED') {
          // For fixed amount discounts, ensure it doesn't exceed the item total
          // IMPORTANT: Prevent negative prices by capping fixed discounts at the item total
          // This validation is critical for financial integrity and is tested in the
          // regression test: "should prevent negative prices by capping discounts"
          itemDiscountAmount = Math.min(itemTotal, item.discountValue);
        }
      }

      const finalItemTotal = itemTotal - itemDiscountAmount;
      subtotal += finalItemTotal;

      processedItems.push({
        productId: item.productId,
        unitId: item.unitId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: itemTotal,
        discountType: item.discountType,
        discountValue: item.discountValue,
        discountAmount: itemDiscountAmount,
        notes: item.notes,
      });
    }

    // Calculate sale-level discount
    let saleDiscountAmount = 0;
    if (createSaleDto.discountType && createSaleDto.discountValue) {
      if (createSaleDto.discountType === 'PERCENTAGE') {
        // For percentage discounts, round to nearest whole number
        // IMPORTANT: Prevent negative prices by capping percentage discounts at 100%
        // This validation is critical for financial integrity and is tested in the
        // regression test: "should prevent negative prices by capping discounts"
        const discountPercentage = Math.min(100, createSaleDto.discountValue);
        saleDiscountAmount = Math.round((subtotal * discountPercentage) / 100);
      } else if (createSaleDto.discountType === 'FIXED_AMOUNT' || createSaleDto.discountType === 'FIXED') {
        // For fixed amount discounts, ensure it doesn't exceed the subtotal
        // IMPORTANT: Prevent negative prices by capping fixed discounts at the subtotal
        // This validation is critical for financial integrity and is tested in the
        // regression test: "should prevent negative prices by capping discounts"
        saleDiscountAmount = Math.min(subtotal, createSaleDto.discountValue);
      }
    }

    const afterDiscount = subtotal - saleDiscountAmount;
    const taxAmount = createSaleDto.taxAmount || 0;
    const totalAmount = afterDiscount + taxAmount;

    // subtotal should remain the raw total before sale-level discounts
    // totalAmount is the final amount after all discounts and taxes

    // Calculate change (for draft sales, we allow insufficient payment)
    const changeAmount = createSaleDto.amountPaid - totalAmount;
    // Note: For draft sales, we don't validate payment sufficiency
    // Payment validation will be done when completing the sale

    // Create draft sale in transaction
    return this.prisma.$transaction(async (tx) => {
      // Create the sale as DRAFT
      const sale = await tx.sale.create({
        data: {
          saleNumber: createSaleDto.saleNumber!,
          customerId: createSaleDto.customerId || null,
          cashierId: createSaleDto.cashierId!,
          saleDate: createSaleDto.saleDate ? new Date(createSaleDto.saleDate) : new Date(),
          subtotal: subtotal,
          discountType: createSaleDto.discountType,
          discountValue: createSaleDto.discountValue,
          discountAmount: saleDiscountAmount,
          taxAmount,
          totalAmount,
          paymentMethod: createSaleDto.paymentMethod,
          amountPaid: createSaleDto.amountPaid,
          changeAmount,
          customerName: createSaleDto.customerName,
          customerPhone: createSaleDto.customerPhone,
          notes: createSaleDto.notes,
          status: SaleStatus.DRAFT, // Keep as draft
        },
      });

      // Create sale items (without stock allocation)
      for (const item of processedItems) {
        await tx.saleItem.create({
          data: {
            saleId: sale.id,
            productId: item.productId,
            unitId: item.unitId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            totalPrice: item.totalPrice,
            discountType: item.discountType,
            discountValue: item.discountValue,
            discountAmount: item.discountAmount,
            notes: item.notes,
          },
        });
      }

      // Fetch the created sale with all relations using the transaction client
      const createdSale = await tx.sale.findUnique({
        where: { id: sale.id },
        include: {
          customer: {
            select: {
              id: true,
              code: true,
              fullName: true,
              phoneNumber: true,
              email: true,
              type: true,
            },
          },
          cashier: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          saleItems: {
            include: {
              product: {
                select: {
                  id: true,
                  code: true,
                  name: true,
                  type: true,
                  manufacturer: true,
                },
              },
              unit: {
                select: {
                  id: true,
                  name: true,
                  abbreviation: true,
                },
              },
            },
          },
        },
      });

      // Convert Decimal fields to numbers for API response
      if (!createdSale) {
        throw new Error('Failed to create sale');
      }

      return {
        ...createdSale,
        subtotal: Number(createdSale.subtotal),
        discountValue: createdSale.discountValue ? Number(createdSale.discountValue) : null,
        discountAmount: Number(createdSale.discountAmount),
        taxAmount: Number(createdSale.taxAmount),
        totalAmount: Number(createdSale.totalAmount),
        amountPaid: Number(createdSale.amountPaid),
        changeAmount: Number(createdSale.changeAmount),
        saleItems: createdSale.saleItems.map(item => ({
          ...item,
          unitPrice: Number(item.unitPrice),
          totalPrice: Number(item.totalPrice),
          discountValue: item.discountValue ? Number(item.discountValue) : null,
          discountAmount: Number(item.discountAmount),
        })),
      };
    });
  }

  /**
   * Complete a draft sale by allocating stock and changing status to COMPLETED
   */
  async completeSale(saleId: string, userId: string) {
    // Get the draft sale
    const sale = await this.prisma.sale.findUnique({
      where: { id: saleId },
      include: {
        saleItems: {
          include: {
            product: true,
            unit: true,
          },
        },
        customer: true,
      },
    });

    if (!sale) {
      throw new NotFoundException(`Transaksi dengan ID ${saleId} tidak ditemukan`);
    }

    if (sale.status !== SaleStatus.DRAFT) {
      throw new ConflictException(`Hanya transaksi draft yang dapat diselesaikan`);
    }

    // Validate stock availability before completing the sale
    // Convert sale items to the format expected by validateStockAvailability
    const itemsForValidation = sale.saleItems.map(item => ({
      productId: item.productId,
      unitId: item.unitId,
      quantity: item.quantity,
    }));

    await this.validateStockAvailability(itemsForValidation);

    // Validate payment amount is sufficient - use precise decimal handling
    const totalAmount = Number(sale.totalAmount);
    const amountPaid = Number(sale.amountPaid);

    // Use a small epsilon to account for floating point precision issues
    const epsilon = 0.01;
    if (totalAmount - amountPaid > epsilon) {
      throw new BadRequestException(`Jumlah pembayaran tidak mencukupi untuk menyelesaikan transaksi`);
    }

    // Execute transaction to complete the sale
    return this.prisma.$transaction(async (tx) => {
      // Allocate stock for all items
      for (const item of sale.saleItems) {
        const product = await this.productsService.findOne(item.productId);
        const method = product.type === 'MEDICINE' ? 'FEFO' : 'FIFO';

        // Get product unit hierarchy to determine conversion factor
        const unitHierarchy = product.unitHierarchies?.find(h => h.unitId === item.unitId);

        let quantityToAllocate = item.quantity;

        // If unit hierarchy exists, convert to base units
        if (unitHierarchy) {
          const conversionFactor = Number(unitHierarchy.conversionFactor);

          // Validate available stock once more before allocation
          const availableStock = await this.inventoryService.getAvailableStock(item.productId, false);

          // Calculate how many whole units are available
          const availableWholeUnits = Math.floor(availableStock / conversionFactor);

          if (availableWholeUnits < item.quantity) {
            throw new BadRequestException(
              `Stok tidak mencukupi untuk ${product.name}. Tersedia: ${availableWholeUnits} ${unitHierarchy.unit?.name || 'unit'}`
            );
          }

          // Convert to base units for allocation
          quantityToAllocate = item.quantity * conversionFactor;
        } else {
          // Validate stock for non-hierarchical units
          const availableStock = await this.inventoryService.getAvailableStock(item.productId, false);
          if (availableStock < item.quantity) {
            throw new BadRequestException(`Stok tidak mencukupi untuk ${product.name}. Tersedia: ${availableStock}, Dibutuhkan: ${item.quantity}`);
          }
        }

        try {
          const allocation = await this.inventoryService.allocateStock(
            item.productId,
            quantityToAllocate,
            method,
            {
              allowPartialAllocation: false, // Sales require exact quantity
              userId: userId,
              reason: `Penjualan #${sale.saleNumber}`,
              notes: `Pelanggan: ${sale.customerName}${sale.customer?.type === CustomerType.WALK_IN ? ' (Walk-in)' : ''}`,
              referenceType: ReferenceType.SALE,
              referenceId: saleId,
              referenceNumber: sale.saleNumber
            },
            tx,
          );

          if (!allocation.success) {
            throw new BadRequestException(`Gagal mengalokasi stok untuk ${product.name}`);
          }
        } catch (error) {
          // Re-throw with more descriptive message
          throw new BadRequestException(`Stok tidak mencukupi untuk ${product.name}: ${error.message}`);
        }
      }

      // Update sale status to COMPLETED
      const completedSale = await tx.sale.update({
        where: { id: saleId },
        data: {
          status: SaleStatus.COMPLETED,
          updatedAt: new Date(),
        },
      });

      // Convert Decimal fields to numbers for API response
      return {
        ...completedSale,
        subtotal: Number(completedSale.subtotal),
        discountAmount: Number(completedSale.discountAmount),
        taxAmount: Number(completedSale.taxAmount),
        totalAmount: Number(completedSale.totalAmount),
        amountPaid: Number(completedSale.amountPaid),
        changeAmount: Number(completedSale.changeAmount),
        discountValue: completedSale.discountValue ? Number(completedSale.discountValue) : null,
      };
    });
  }

  /**
   * Cancel a sale and restore stock if it was allocated
   */
  async cancelSale(saleId: string, userId: string, reason?: string) {
    const sale = await this.prisma.sale.findUnique({
      where: { id: saleId },
      include: {
        saleItems: true,
      },
    });

    if (!sale) {
      throw new NotFoundException(`Transaksi dengan ID ${saleId} tidak ditemukan`);
    }

    if (sale.status === SaleStatus.CANCELLED) {
      throw new ConflictException('Transaksi sudah dibatalkan');
    }

    if (sale.status === SaleStatus.REFUNDED) {
      throw new ConflictException('Transaksi yang sudah di-refund tidak dapat dibatalkan');
    }

    return this.prisma.$transaction(async (tx) => {
      // If sale was completed, we need to restore stock
      if (sale.status === SaleStatus.COMPLETED) {
        // Check if there are any OUT stock movements for this sale
        // If there are, we need to restore the physical stock
        const outStockMovements = await tx.stockMovement.findMany({
          where: {
            referenceId: saleId,
            type: StockMovementType.OUT
          },
          include: {
            inventoryItem: true
          }
        });

        if (outStockMovements.length > 0) {
          // Stock has been physically dispensed, so we need to restore it
          for (const movement of outStockMovements) {
            // Create an IN movement to reverse the OUT movement
            await tx.stockMovement.create({
              data: {
                inventoryItemId: movement.inventoryItemId,
                type: StockMovementType.IN,
                quantity: Math.abs(movement.quantity), // Convert negative quantity to positive
                unitPrice: movement.unitPrice,
                referenceType: ReferenceType.SALE_CANCELLATION,
                referenceId: saleId,
                referenceNumber: sale.saleNumber,
                reason: `Pembatalan transaksi: ${reason || 'Tidak ada alasan'}`,
                notes: `Pengembalian stok dari penjualan ${sale.saleNumber}`,
                movementDate: new Date(),
                createdBy: userId
              }
            });

            // Update inventory item to restore physical stock
            await tx.inventoryItem.update({
              where: { id: movement.inventoryItemId },
              data: {
                quantityOnHand: {
                  increment: Math.abs(movement.quantity) // Restore the physical stock
                }
              }
            });

            // Also create a negative allocation movement to properly track stock restoration
            await tx.stockMovement.create({
              data: {
                inventoryItemId: movement.inventoryItemId,
                type: StockMovementType.ALLOCATION,
                quantity: -Math.abs(movement.quantity), // Negative for deallocation
                unitPrice: movement.unitPrice,
                referenceType: ReferenceType.SALE_CANCELLATION,
                referenceId: saleId,
                referenceNumber: sale.saleNumber,
                reason: `Dealokasi stok untuk pembatalan transaksi: ${reason || 'Tidak ada alasan'}`,
                notes: `Dealokasi stok dari penjualan ${sale.saleNumber}`,
                movementDate: new Date(),
                createdBy: userId
              }
            });
          }
        } else {
          // No OUT movements found, so stock was only allocated but not dispensed
          // Restore stock by deallocating the quantity that was allocated during sale completion
          for (const item of sale.saleItems) {
            // Find all inventory items with allocated stock for this product
            const inventoryItems = await tx.inventoryItem.findMany({
              where: {
                productId: item.productId,
                isActive: true
              }
            });

            // Check if any items are allocated
            const allocatedItems = inventoryItems.filter(invItem => invItem.quantityAllocated > 0);

            if (allocatedItems.length > 0) {
              let remainingToDeallocate = item.quantity;

              for (const invItem of allocatedItems) {
                if (remainingToDeallocate <= 0) break;

                const amountToDeallocate = Math.min(remainingToDeallocate, invItem.quantityAllocated);

                if (amountToDeallocate > 0) {
                  // Update inventory item to reduce allocated quantity
                  await tx.inventoryItem.update({
                    where: { id: invItem.id },
                    data: {
                      quantityAllocated: {
                        decrement: amountToDeallocate
                      }
                    }
                  });

                  // Create allocation movement with negative quantity
                  await tx.stockMovement.create({
                    data: {
                      inventoryItemId: invItem.id,
                      type: StockMovementType.ALLOCATION,
                      quantity: -amountToDeallocate, // Negative for deallocation
                      unitPrice: invItem.costPrice,
                      referenceType: ReferenceType.SALE_CANCELLATION,
                      referenceId: saleId,
                      referenceNumber: sale.saleNumber,
                      reason: `Dealokasi stok untuk pembatalan transaksi: ${reason || 'Tidak ada alasan'}`,
                      notes: `Dealokasi stok dari penjualan ${sale.saleNumber}`,
                      movementDate: new Date(),
                      createdBy: userId
                    }
                  });

                  remainingToDeallocate -= amountToDeallocate;
                }
              }

              // If we couldn't deallocate all the quantity, log a warning
              if (remainingToDeallocate > 0) {
                console.warn(`Warning: Could not deallocate all stock for product ${item.productId}. Remaining: ${remainingToDeallocate}`);
              }
            } else {
              // If no allocated items found, try to find items with stock and create deallocation movements
              const itemsWithStock = inventoryItems.filter(invItem => invItem.quantityOnHand > 0);

              if (itemsWithStock.length > 0) {
                let remainingToDeallocate = item.quantity;

                for (const invItem of itemsWithStock) {
                  if (remainingToDeallocate <= 0) break;

                  const amountToDeallocate = Math.min(remainingToDeallocate, invItem.quantityOnHand);

                  if (amountToDeallocate > 0) {
                    // Create allocation movement with negative quantity
                    await tx.stockMovement.create({
                      data: {
                        inventoryItemId: invItem.id,
                        type: StockMovementType.ALLOCATION,
                        quantity: -amountToDeallocate, // Negative for deallocation
                        unitPrice: invItem.costPrice,
                        referenceType: ReferenceType.SALE_CANCELLATION,
                        referenceId: saleId,
                        referenceNumber: sale.saleNumber,
                        reason: `Dealokasi stok untuk pembatalan transaksi: ${reason || 'Tidak ada alasan'}`,
                        notes: `Dealokasi stok dari penjualan ${sale.saleNumber}`,
                        movementDate: new Date(),
                        createdBy: userId
                      }
                    });

                    remainingToDeallocate -= amountToDeallocate;
                  }
                }
              } else {
                // Fallback to the deallocateStock method if no items found
                await this.inventoryService.deallocateStock(
                  item.productId,
                  item.quantity,
                  {
                    referenceType: ReferenceType.SALE_CANCELLATION,
                    referenceId: saleId,
                    referenceNumber: sale.saleNumber,
                    reason: `Pembatalan transaksi: ${reason || 'Tidak ada alasan'}`,
                    userId: userId || 'system',
                  }
                );
              }
            }
          }
        }
      }

      // Update sale status to CANCELLED
      const cancelledSale = await tx.sale.update({
        where: { id: saleId },
        data: {
          status: SaleStatus.CANCELLED,
          notes: reason ? `${sale.notes || ''}\nDibatalkan: ${reason}`.trim() : sale.notes,
          updatedAt: new Date(),
        },
      });

      // Fetch the cancelled sale with all relations using the transaction client
      return tx.sale.findUnique({
        where: { id: cancelledSale.id },
        include: {
          customer: {
            select: {
              id: true,
              code: true,
              fullName: true,
              phoneNumber: true,
              email: true,
              type: true,
            },
          },
          cashier: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          saleItems: {
            include: {
              product: {
                select: {
                  id: true,
                  code: true,
                  name: true,
                  type: true,
                  manufacturer: true,
                },
              },
              unit: {
                select: {
                  id: true,
                  name: true,
                  abbreviation: true,
                },
              },
            },
          },
        },
      });
    });
  }

  /**
   * Refund a completed sale
   * This will deallocate any allocated stock and restore any consumed stock
   */
  async refundSale(saleId: string, userId: string, reason?: string) {
    const sale = await this.findOne(saleId);

    if (sale.status !== SaleStatus.COMPLETED) {
      throw new ConflictException('Hanya transaksi yang sudah selesai yang dapat di-refund');
    }

    return this.prisma.$transaction(async (tx) => {
      // Check if there are any OUT stock movements for this sale
      // If there are, we need to restore the physical stock
      const outStockMovements = await tx.stockMovement.findMany({
        where: {
          referenceId: saleId,
          type: StockMovementType.OUT
        }
      });

      if (outStockMovements.length > 0) {
        // Stock has been physically dispensed, so we need to restore it
        for (const movement of outStockMovements) {
          // Create an IN movement to reverse the OUT movement
          await tx.stockMovement.create({
            data: {
              inventoryItemId: movement.inventoryItemId,
              type: StockMovementType.IN,
              quantity: Math.abs(movement.quantity), // Convert negative quantity to positive
              unitPrice: movement.unitPrice,
              referenceType: ReferenceType.SALE_REFUND,
              referenceId: saleId,
              referenceNumber: sale.saleNumber,
              reason: `Refund transaksi: ${reason || 'Tidak ada alasan'}`,
              notes: `Pengembalian stok dari penjualan ${sale.saleNumber}`,
              movementDate: new Date(),
              createdBy: userId
            }
          });

          // Update inventory item to restore physical stock
          await tx.inventoryItem.update({
            where: { id: movement.inventoryItemId },
            data: {
              quantityOnHand: {
                increment: Math.abs(movement.quantity) // Restore the physical stock
              }
            }
          });
        }
      } else {
        // No OUT movements found, so stock was only allocated but not dispensed
        // Restore stock by deallocating the quantity that was allocated during sale completion
        for (const item of sale.saleItems) {
          await this.inventoryService.deallocateStock(
            item.productId,
            item.quantity,
            {
              referenceType: ReferenceType.SALE_REFUND,
              referenceId: saleId,
              referenceNumber: sale.saleNumber,
              reason: `Refund transaksi: ${reason || 'Tidak ada alasan'}`,
              userId: userId || 'system',
            }
          );
        }
      }

      // Update sale status to REFUNDED
      const refundedSale = await tx.sale.update({
        where: { id: saleId },
        data: {
          status: SaleStatus.REFUNDED,
          notes: reason ? `${sale.notes || ''}\nDi-refund: ${reason}`.trim() : sale.notes,
          updatedAt: new Date(),
        },
      });

      // Fetch the refunded sale with all relations using the transaction client
      return tx.sale.findUnique({
        where: { id: refundedSale.id },
        include: {
          customer: {
            select: {
              id: true,
              code: true,
              fullName: true,
              phoneNumber: true,
              email: true,
              type: true,
            },
          },
          cashier: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          saleItems: {
            include: {
              product: {
                select: {
                  id: true,
                  code: true,
                  name: true,
                  type: true,
                  manufacturer: true,
                },
              },
              unit: {
                select: {
                  id: true,
                  name: true,
                  abbreviation: true,
                },
              },
            },
          },
        },
      });
    });
  }

  /**
   * Update a draft sale with full support for item updates and total recalculation
   */
  async updateDraft(saleId: string, updateSaleDto: UpdateSaleDto) {
    const sale = await this.findOne(saleId);

    if (sale.status !== SaleStatus.DRAFT) {
      throw new ConflictException('Hanya transaksi draft yang dapat diubah');
    }

    // If items are being updated, we need to recalculate totals
    if (updateSaleDto.items) {
      return this.updateDraftWithItems(saleId, updateSaleDto);
    }

    // If only sale-level fields are being updated, use simple update
    return this.updateDraftSaleFields(saleId, updateSaleDto);
  }

  /**
   * Update draft sale with item changes and full recalculation
   */
  private async updateDraftWithItems(saleId: string, updateSaleDto: UpdateSaleDto) {
    // Get existing sale to verify it's a draft
    const existingSale = await this.findOne(saleId);
    if (existingSale.status !== SaleStatus.DRAFT) {
      throw new BadRequestException('Hanya draft transaksi yang dapat diperbarui dengan item baru');
    }

    // First, update the sale fields (this does not touch items)
    await this.updateDraftSaleFields(saleId, updateSaleDto);

    // Then handle the items if provided
    if (updateSaleDto.items) {
      // Validate and process the items
      const processedItems: Array<{
        productId: string;
        unitId: string;
        quantity: number;
        unitPrice: number;
        totalPrice: number;
        discountType?: string;
        discountValue?: number;
        discountAmount: number;
        notes?: string;
      }> = [];

      for (const item of updateSaleDto.items) {
        // Validate product exists
        const product = await this.productsService.findOne(item.productId);

        // Get product unit hierarchy to determine conversion factor
        const unitHierarchy = product.unitHierarchies?.find(h => h.unitId === item.unitId);

        // If we have a unit hierarchy, check stock (warn only for drafts)
        let availableWholeUnits = 0;
        if (unitHierarchy) {
          // Calculate base unit quantity needed
          const conversionFactor = Number(unitHierarchy.conversionFactor);

          // Check stock availability before processing
          const availableStock = await this.inventoryService.getAvailableStock(item.productId, false);

          // Calculate how many whole units are available
          availableWholeUnits = Math.floor(availableStock / conversionFactor);

          if (availableWholeUnits < item.quantity) {
            // For draft sales, we just log a warning but allow update
            console.warn(`Warning: Insufficient stock for ${product.name}. Available: ${availableWholeUnits} ${unitHierarchy.unit?.name || 'unit'}, Requested: ${item.quantity}`);
          }
        } else {
          // Fallback to simple validation when unit hierarchy is missing (for tests)
          const availableStock = await this.inventoryService.getAvailableStock(item.productId, false);
          if (availableStock < item.quantity) {
            // For draft sales, we just log a warning but allow update
            console.warn(`Warning: Insufficient stock for ${product.name}. Available: ${availableStock}, Requested: ${item.quantity}`);
          }
          availableWholeUnits = Math.floor(availableStock);
        }

        // Calculate item totals using a consistent formula for all cases
        const itemSubtotal = item.quantity * item.unitPrice;
        let itemDiscountAmount = 0;

        // Handle the different discount types
        if (item.discountType && item.discountValue) {
          if (item.discountType === 'PERCENTAGE') {
            // For percentage discounts, apply the discount to the subtotal
            // IMPORTANT: Prevent negative prices by capping percentage discounts at 100%
            // This validation is critical for financial integrity and is tested in the
            // regression test: "should prevent negative prices by capping discounts"
            const discountPercentage = Math.min(100, item.discountValue);
            itemDiscountAmount = Math.round((itemSubtotal * discountPercentage) / 100);
          } else if (item.discountType === 'FIXED_AMOUNT' || item.discountType === 'FIXED') {
            // For fixed amount discounts, ensure it doesn't exceed the item subtotal
            // IMPORTANT: Prevent negative prices by capping fixed discounts at the item subtotal
            // This validation is critical for financial integrity and is tested in the
            // regression test: "should prevent negative prices by capping discounts"
            itemDiscountAmount = Math.min(itemSubtotal, item.discountValue);
          }
        }

        // Calculate the final item price after discounts
        const finalItemPrice = itemSubtotal - itemDiscountAmount;

        processedItems.push({
          productId: item.productId,
          unitId: item.unitId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: finalItemPrice,
          discountType: item.discountType,
          discountValue: item.discountValue,
          discountAmount: itemDiscountAmount,
          notes: item.notes,
        });
      }

      // Calculate sale-level discount
      // First calculate the subtotal after item-level discounts
      let subtotal = 0;
      for (const item of processedItems) {
        // Add each item's total to the subtotal
        subtotal += item.totalPrice;
      }

      // No special case handling - all calculations should be consistent
      let saleDiscountAmount = 0;
      if (updateSaleDto.discountType && updateSaleDto.discountValue) {
        if (updateSaleDto.discountType === 'PERCENTAGE') {
          // IMPORTANT: Prevent negative prices by capping percentage discounts at 100%
          // This validation is critical for financial integrity and is tested in the
          // regression test: "should prevent negative prices by capping discounts"
          const discountPercentage = Math.min(100, updateSaleDto.discountValue);
          saleDiscountAmount = Math.round((subtotal * discountPercentage) / 100);
        } else if (updateSaleDto.discountType === 'FIXED_AMOUNT' || updateSaleDto.discountType === 'FIXED') {
          // IMPORTANT: Prevent negative prices by capping fixed discounts at the subtotal
          // This validation is critical for financial integrity and is tested in the
          // regression test: "should prevent negative prices by capping discounts"
          saleDiscountAmount = Math.min(subtotal, updateSaleDto.discountValue);
        }
      }

      const afterDiscount = subtotal - saleDiscountAmount;
      const taxAmount = updateSaleDto.taxAmount || 0;
      const totalAmount = afterDiscount + taxAmount;

      // Calculate change (for draft sales, we allow insufficient payment)
      const amountPaid = updateSaleDto.amountPaid || 0;
      const changeAmount = amountPaid - totalAmount;

      // Update draft sale in transaction
      return this.prisma.$transaction(async (tx) => {
        // Delete existing sale items
        await tx.saleItem.deleteMany({
          where: { saleId },
        });

        // Update the sale with new totals
        const updatedSale = await tx.sale.update({
          where: { id: saleId },
          data: {
            customerId: updateSaleDto.customerId || null,
            subtotal: subtotal,
            discountType: updateSaleDto.discountType,
            discountValue: updateSaleDto.discountValue,
            discountAmount: saleDiscountAmount,
            taxAmount,
            totalAmount,
            paymentMethod: updateSaleDto.paymentMethod,
            amountPaid,
            changeAmount,
            customerName: updateSaleDto.customerName,
            customerPhone: updateSaleDto.customerPhone,
            notes: updateSaleDto.notes,
            updatedAt: new Date(),
          },
        });

        // Create new sale items
        for (const item of processedItems) {
          await tx.saleItem.create({
            data: {
              saleId: updatedSale.id,
              productId: item.productId,
              unitId: item.unitId,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              totalPrice: item.totalPrice,
              discountType: item.discountType,
              discountValue: item.discountValue,
              discountAmount: item.discountAmount,
              notes: item.notes,
            },
          });
        }

        // Fetch the updated sale with all relations
        const updatedSaleWithRelations = await tx.sale.findUnique({
          where: { id: updatedSale.id },
          include: {
            customer: {
              select: {
                id: true,
                code: true,
                fullName: true,
                phoneNumber: true,
                email: true,
              },
            },
            saleItems: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    code: true,
                  },
                },
                unit: {
                  select: {
                    id: true,
                    name: true,
                    abbreviation: true,
                  },
                },
              },
            },
          },
        });

        if (!updatedSaleWithRelations) {
          throw new NotFoundException(`Sale with ID ${updatedSale.id} not found after update`);
        }

        // Convert Decimal fields to numbers for API response
        return {
          ...updatedSaleWithRelations,
          subtotal: Number(updatedSaleWithRelations.subtotal),
          discountAmount: Number(updatedSaleWithRelations.discountAmount),
          taxAmount: Number(updatedSaleWithRelations.taxAmount),
          totalAmount: Number(updatedSaleWithRelations.totalAmount),
          amountPaid: Number(updatedSaleWithRelations.amountPaid),
          changeAmount: Number(updatedSaleWithRelations.changeAmount),
          discountValue: updatedSaleWithRelations.discountValue ? Number(updatedSaleWithRelations.discountValue) : null,
          saleItems: updatedSaleWithRelations.saleItems.map((item) => ({
            ...item,
            unitPrice: Number(item.unitPrice),
            totalPrice: Number(item.totalPrice),
            discountAmount: Number(item.discountAmount),
            discountValue: item.discountValue ? Number(item.discountValue) : null,
          })),
        };
      });
    }
  }

  /**
   * Update only sale-level fields without touching items
   */
  private async updateDraftSaleFields(saleId: string, updateSaleDto: UpdateSaleDto) {
    // Calculate change amount if amountPaid is being updated
    let changeAmount: number | undefined;
    if (updateSaleDto.amountPaid !== undefined) {
      const sale = await this.prisma.sale.findUnique({ where: { id: saleId } });
      if (sale) {
        changeAmount = updateSaleDto.amountPaid - Number(sale.totalAmount);
      }
    }

    const updatedSale = await this.prisma.sale.update({
      where: { id: saleId },
      data: {
        customerId: updateSaleDto.customerId,
        customerName: updateSaleDto.customerName,
        customerPhone: updateSaleDto.customerPhone,
        notes: updateSaleDto.notes,
        paymentMethod: updateSaleDto.paymentMethod,
        amountPaid: updateSaleDto.amountPaid,
        changeAmount: changeAmount,
        updatedAt: new Date(),
      },
    });

    // Convert Decimal fields to numbers for API response
    return {
      ...updatedSale,
      subtotal: Number(updatedSale.subtotal),
      discountValue: updatedSale.discountValue ? Number(updatedSale.discountValue) : null,
      discountAmount: Number(updatedSale.discountAmount),
      taxAmount: Number(updatedSale.taxAmount),
      totalAmount: Number(updatedSale.totalAmount),
      amountPaid: Number(updatedSale.amountPaid),
      changeAmount: Number(updatedSale.changeAmount),
    };
  }

  /**
   * Delete a sale and deallocate stock if it was allocated
   * This permanently removes the sale record and related items
   */
  async deleteSale(saleId: string, userId: string, reason?: string) {
    const sale = await this.findOne(saleId);

    if (!sale) {
      throw new NotFoundException('Transaksi tidak ditemukan');
    }

    if (sale.status === SaleStatus.COMPLETED) {
      throw new ConflictException('Transaksi yang sudah selesai tidak dapat dihapus. Gunakan fitur pembatalan atau refund.');
    }

    // Use transaction to ensure atomicity
    return this.prisma.$transaction(async (tx) => {
      // If sale was not a draft, we need to deallocate any stock that might have been allocated
      if (sale.status !== SaleStatus.DRAFT) {
        for (const item of sale.saleItems) {
          try {
            // Restore stock by deallocating the quantity
            await this.inventoryService.deallocateStock(
              item.productId,
              item.quantity,
              {
                referenceType: ReferenceType.SALE_DELETION,
                referenceId: saleId,
                referenceNumber: sale.saleNumber,
                reason: `Penghapusan transaksi: ${reason || 'Tidak ada alasan'}`,
                userId: userId || 'system',
              }
            );
          } catch (error) {
            // Log error but continue with deletion
            console.error(`Failed to deallocate stock for product ${item.productId} in sale ${saleId}:`, error.message);
          }
        }
      }

      // Delete sale items (cascade delete should handle this automatically)
      await tx.saleItem.deleteMany({
        where: { saleId },
      });

      // Delete the sale record
      await tx.sale.delete({
        where: { id: saleId },
      });

      return {
        success: true,
        message: `Transaksi ${sale.saleNumber} berhasil dihapus`,
        deletedSaleId: saleId,
        saleNumber: sale.saleNumber,
      };
    });
  }

  /**
   * Delete multiple sales at once (bulk delete)
   * Will only delete sales that are in DRAFT or CANCELLED status
   */
  async bulkDeleteSales(saleIds: string[], userId: string, reason?: string) {
    if (!saleIds || !Array.isArray(saleIds) || saleIds.length === 0) {
      throw new BadRequestException('Minimal satu transaksi harus dipilih untuk dihapus');
    }

    const results = {
      deleted: 0,
      failed: 0,
      errors: [] as string[],
    };

    // Process sales one by one to handle errors gracefully
    for (const saleId of saleIds) {
      try {
        const sale = await this.findOne(saleId);

        // Only allow deleting drafts or cancelled sales
        if (sale.status === SaleStatus.COMPLETED || sale.status === SaleStatus.REFUNDED) {
          results.failed++;
          results.errors.push(`Transaksi ${sale.saleNumber} tidak dapat dihapus karena statusnya ${sale.status}`);
          continue;
        }

        // Delete the sale
        await this.deleteSale(saleId, userId, `${reason || 'Bulk deletion'} - ID: ${saleId}`);
        results.deleted++;
      } catch (error) {
        results.failed++;
        results.errors.push(`Gagal menghapus transaksi ID ${saleId}: ${error.message}`);
      }
    }

    return results;
  }

  /**
   * Consumes allocated stock for a completed sale
   * This method is used to decrease physical stock (quantityOnHand) for items that have been dispensed to the customer
   * 
   * @param saleId - ID of the sale to consume stock for
   * @param userId - ID of the user performing the consumption
   * @param notes - Optional notes for the stock movement
   * @param tx - Optional transaction object to use instead of creating a new one
   * @returns Object with success status and details of consumed stock
   */
  async consumeSaleStock(saleId: string, userId: string, notes?: string, tx?: any) {
    // First, validate the sale exists and is in COMPLETED status
    const prismaClient = tx || this.prisma;

    const sale = await prismaClient.sale.findUnique({
      where: { id: saleId },
    });

    if (!sale) {
      throw new NotFoundException(`Penjualan dengan ID ${saleId} tidak ditemukan`);
    }

    if (sale.status !== SaleStatus.COMPLETED) {
      throw new BadRequestException(
        `Tidak dapat mengkonsumsi stok untuk penjualan dengan status ${sale.status}. Status harus COMPLETED.`
      );
    }

    // Use the stock consumption service to consume the allocated stock
    return this.stockConsumptionService.consumeAllocatedStock(saleId, userId, notes, tx);
  }

  /**
   * Get sales for a specific cashier's session on a given date
   */
  async getCashierSessionSales(cashierId: string, sessionDate: string) {
    const startOfDay = new Date(sessionDate);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(sessionDate);
    endOfDay.setHours(23, 59, 59, 999);

    const sales = await this.prisma.sale.findMany({
      where: {
        cashierId,
        saleDate: {
          gte: startOfDay,
          lte: endOfDay,
        },
        status: SaleStatus.COMPLETED,
      },
      include: {
        customer: {
          select: {
            id: true,
            fullName: true,
            code: true,
            type: true,
          },
        },
        saleItems: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                code: true,
              },
            },
            unit: {
              select: {
                id: true,
                name: true,
                abbreviation: true,
              },
            },
          },
        },
      },
      orderBy: {
        saleDate: 'desc',
      },
    });

    return {
      data: sales,
      meta: {
        total: sales.length,
        sessionDate,
        cashierId,
      },
    };
  }

  /**
   * Get session statistics for a specific cashier on a given date
   */
  async getSessionStats(cashierId: string, sessionDate: string) {
    const startOfDay = new Date(sessionDate);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(sessionDate);
    endOfDay.setHours(23, 59, 59, 999);

    // Get all completed sales for the session
    const sales = await this.prisma.sale.findMany({
      where: {
        cashierId,
        saleDate: {
          gte: startOfDay,
          lte: endOfDay,
        },
        status: SaleStatus.COMPLETED,
      },
      select: {
        id: true,
        totalAmount: true,
        paymentMethod: true,
        saleDate: true,
      },
    });

    // Calculate statistics
    const totalSalesCount = sales.length;
    const totalRevenue = sales.reduce((sum, sale) => sum + Number(sale.totalAmount), 0);
    const averageTransactionValue = totalSalesCount > 0 ? totalRevenue / totalSalesCount : 0;

    // Payment method breakdown
    const paymentMethodBreakdown = sales.reduce((acc, sale) => {
      const method = sale.paymentMethod;
      if (!acc[method]) {
        acc[method] = { count: 0, amount: 0 };
      }
      acc[method].count += 1;
      acc[method].amount += Number(sale.totalAmount);
      return acc;
    }, {} as Record<string, { count: number; amount: number }>);

    // Hourly breakdown
    const hourlyBreakdown = sales.reduce((acc, sale) => {
      const hour = sale.saleDate.getHours();
      if (!acc[hour]) {
        acc[hour] = { count: 0, amount: 0 };
      }
      acc[hour].count += 1;
      acc[hour].amount += Number(sale.totalAmount);
      return acc;
    }, {} as Record<number, { count: number; amount: number }>);

    return {
      sessionDate,
      cashierId,
      totalSalesCount,
      totalRevenue,
      averageTransactionValue,
      paymentMethodBreakdown,
      hourlyBreakdown,
      firstSaleTime: sales.length > 0 ? Math.min(...sales.map(s => s.saleDate.getTime())) : null,
      lastSaleTime: sales.length > 0 ? Math.max(...sales.map(s => s.saleDate.getTime())) : null,
    };
  }

  /**
   * Get sales for a specific session based on session start time
   */
  async getSessionSales(cashierId: string, sessionStartTime: string, sessionEndTime?: string) {
    // Validate and parse session start time
    const sessionStart = new Date(sessionStartTime);
    if (isNaN(sessionStart.getTime())) {
      throw new Error('Invalid session start time format');
    }

    // Validate and parse session end time if provided
    let sessionEnd: Date;
    if (sessionEndTime) {
      sessionEnd = new Date(sessionEndTime);
      if (isNaN(sessionEnd.getTime())) {
        throw new Error('Invalid session end time format');
      }
    } else {
      sessionEnd = new Date(); // Use current time for active sessions
    }

    const sales = await this.prisma.sale.findMany({
      where: {
        cashierId,
        saleDate: {
          gte: sessionStart,
          lte: sessionEnd,
        },
        status: SaleStatus.COMPLETED,
      },
      include: {
        customer: {
          select: {
            id: true,
            fullName: true,
            code: true,
            type: true,
          },
        },
        saleItems: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                code: true,
              },
            },
            unit: {
              select: {
                id: true,
                name: true,
                abbreviation: true,
              },
            },
          },
        },
      },
      orderBy: {
        saleDate: 'desc',
      },
    });

    return {
      data: sales,
      meta: {
        total: sales.length,
        sessionStartTime,
        cashierId,
      },
    };
  }

  /**
   * Get session statistics based on session start time
   */
  async getSessionStatsById(cashierId: string, sessionStartTime: string, sessionEndTime?: string) {
    // Validate and parse session start time
    const sessionStart = new Date(sessionStartTime);
    if (isNaN(sessionStart.getTime())) {
      throw new Error('Invalid session start time format');
    }

    // Validate and parse session end time if provided
    let sessionEnd: Date;
    if (sessionEndTime) {
      sessionEnd = new Date(sessionEndTime);
      if (isNaN(sessionEnd.getTime())) {
        throw new Error('Invalid session end time format');
      }
    } else {
      sessionEnd = new Date(); // Use current time for active sessions
    }

    // Get all completed sales for the session
    const sales = await this.prisma.sale.findMany({
      where: {
        cashierId,
        saleDate: {
          gte: sessionStart,
          lte: sessionEnd,
        },
        status: SaleStatus.COMPLETED,
      },
      select: {
        id: true,
        totalAmount: true,
        paymentMethod: true,
        saleDate: true,
      },
    });

    // Calculate statistics
    const totalSalesCount = sales.length;
    const totalRevenue = sales.reduce((sum, sale) => {
      const amount = typeof sale.totalAmount === 'string' ? parseFloat(sale.totalAmount) : Number(sale.totalAmount);
      return sum + amount;
    }, 0);
    const averageTransactionValue = totalSalesCount > 0 ? totalRevenue / totalSalesCount : 0;

    // Payment method breakdown
    const paymentMethodBreakdown = sales.reduce((acc, sale) => {
      const method = sale.paymentMethod;
      if (!acc[method]) {
        acc[method] = { count: 0, amount: 0 };
      }
      acc[method].count += 1;
      const amount = typeof sale.totalAmount === 'string' ? parseFloat(sale.totalAmount) : Number(sale.totalAmount);
      acc[method].amount += amount;
      return acc;
    }, {} as Record<string, { count: number; amount: number }>);

    // Hourly breakdown
    const hourlyBreakdown = sales.reduce((acc, sale) => {
      const hour = sale.saleDate.getHours();
      if (!acc[hour]) {
        acc[hour] = { count: 0, amount: 0 };
      }
      acc[hour].count += 1;
      const amount = typeof sale.totalAmount === 'string' ? parseFloat(sale.totalAmount) : Number(sale.totalAmount);
      acc[hour].amount += amount;
      return acc;
    }, {} as Record<number, { count: number; amount: number }>);

    return {
      sessionStartTime,
      cashierId,
      totalSalesCount,
      totalRevenue,
      averageTransactionValue,
      paymentMethodBreakdown,
      hourlyBreakdown,
      firstSaleTime: sales.length > 0 ? Math.min(...sales.map(s => s.saleDate.getTime())) : null,
      lastSaleTime: sales.length > 0 ? Math.max(...sales.map(s => s.saleDate.getTime())) : null,
    };
  }

  /**
   * Comprehensive stock validation with unit conversion support
   * Validates stock availability for all items before any allocation occurs
   */
  private async validateStockAvailability(items: any[]): Promise<void> {
    // Group items by product to handle mixed units correctly
    const productGroups = new Map<string, any[]>();

    for (const item of items) {
      if (!productGroups.has(item.productId)) {
        productGroups.set(item.productId, []);
      }
      productGroups.get(item.productId)!.push(item);
    }

    // Validate each product group
    for (const [productId, productItems] of productGroups) {
      await this.validateProductStockAvailability(productId, productItems);
    }
  }

  /**
   * Validate stock availability for a specific product with multiple units
   */
  private async validateProductStockAvailability(productId: string, items: any[]): Promise<void> {
    try {
      // Get product information
      const product = await this.productsService.findOne(productId);

      // Get unit hierarchies for this product
      const unitHierarchies = await this.prisma.productUnitHierarchy.findMany({
        where: {
          productId,
          isActive: true
        },
        include: {
          unit: true
        }
      });

      if (unitHierarchies.length === 0) {
        throw new BadRequestException(`Tidak ada unit hierarchy yang ditemukan untuk produk ${product.name}`);
      }

      // Find base unit (level 0 or lowest conversion factor)
      const baseUnit = unitHierarchies.find(h => h.level === 0) ||
        unitHierarchies.reduce((min, current) =>
          current.conversionFactor < min.conversionFactor ? current : min
        );

      // Calculate total demand in base units
      let totalDemandInBaseUnits = 0;

      for (const item of items) {
        const unitHierarchy = unitHierarchies.find(h => h.unitId === item.unitId);

        if (!unitHierarchy) {
          throw new BadRequestException(`Unit tidak ditemukan untuk produk ${product.name}`);
        }

        // Convert to base units using the unit conversion service
        const conversionResult = await this.unitConversionService.convertToBaseUnit(
          productId,
          item.unitId,
          item.quantity
        );

        if (!conversionResult.success) {
          throw new BadRequestException(
            `Gagal mengkonversi unit untuk produk ${product.name}: ${conversionResult.error}`
          );
        }

        totalDemandInBaseUnits += conversionResult.convertedQuantity!;
      }

      // Get available stock in base units
      const availableStock = await this.inventoryService.getAvailableStock(productId);



      // Check if we have sufficient stock
      if (availableStock < totalDemandInBaseUnits) {
        throw new BadRequestException(
          `Stok tidak mencukupi untuk produk ${product.name}. ` +
          `Tersedia: ${availableStock} ${baseUnit.unit.name}, ` +
          `Diminta: ${totalDemandInBaseUnits} ${baseUnit.unit.name}`
        );
      }

    } catch (error) {
      if (error.status === 404) {
        throw new NotFoundException(`Produk tidak ditemukan: ${error.message}`);
      }
      throw error;
    }
  }
}
