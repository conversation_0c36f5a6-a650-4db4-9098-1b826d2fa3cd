import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCustomerDto } from './dto/create-customer.dto';
import { UpdateCustomerDto } from './dto/update-customer.dto';
import { CustomerQueryDto } from './dto/customer-query.dto';
import { CustomerCodeGeneratorService } from './customer-code-generator.service';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';

@Injectable()
export class CustomersService {
  constructor(
    private prisma: PrismaService,
    private customerCodeGeneratorService: CustomerCodeGeneratorService,
    @InjectPinoLogger(CustomersService.name)
    private readonly logger: PinoLogger,
  ) { }

  async create(createCustomerDto: CreateCustomerDto, userId: string) {
    // Generate customer code if not provided
    if (!createCustomerDto.code) {
      createCustomerDto.code = await this.customerCodeGeneratorService.generateCustomerCode(
        createCustomerDto.type,
      );
    } else {
      // Validate code uniqueness if provided
      const isUnique = await this.customerCodeGeneratorService.validateCodeUniqueness(
        createCustomerDto.code,
      );
      if (!isUnique) {
        throw new BadRequestException('Kode pelanggan sudah digunakan');
      }
    }

    // Validate membership number uniqueness if provided
    if (createCustomerDto.membershipNumber) {
      const existingMembership = await this.prisma.customer.findUnique({
        where: { membershipNumber: createCustomerDto.membershipNumber },
      });
      if (existingMembership) {
        throw new BadRequestException('Nomor keanggotaan sudah digunakan');
      }

      try {
        return await this.prisma.customer.create({
          data: {
            code: createCustomerDto.code!,
            type: createCustomerDto.type,
            firstName: createCustomerDto.firstName,
            lastName: createCustomerDto.lastName,
            fullName: createCustomerDto.fullName,
            phoneNumber: createCustomerDto.phoneNumber,
            email: createCustomerDto.email,
            dateOfBirth: createCustomerDto.dateOfBirth ? new Date(createCustomerDto.dateOfBirth) : undefined,
            gender: createCustomerDto.gender,
            address: createCustomerDto.address,
            city: createCustomerDto.city,
            province: createCustomerDto.province,
            postalCode: createCustomerDto.postalCode,
            membershipNumber: createCustomerDto.membershipNumber,
            membershipLevel: createCustomerDto.membershipLevel,
            loyaltyPoints: createCustomerDto.loyaltyPoints || 0,
            notes: createCustomerDto.notes,
            isActive: createCustomerDto.isActive ?? true,
            createdBy: userId,
            updatedBy: userId,
          },
          include: {
            createdByUser: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        });
      } catch (error) {
        this.logger.error({
          err: error,
          customerCode: createCustomerDto.code,
          customerName: createCustomerDto.fullName,
          userId
        }, 'Failed to create customer');
        throw error;
      }
    }
  }

  async findAll(query: CustomerQueryDto) {
      const { page = 1, limit = 10, search, type, membershipLevel, isActive, sortBy = 'createdAt', sortOrder = 'desc' } = query;
      const skip = (page - 1) * limit;

      const where: any = {};

      if (search) {
        where.OR = [
          { fullName: { contains: search, mode: 'insensitive' } },
          { phoneNumber: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { code: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (type) {
        where.type = type;
      }

      if (membershipLevel) {
        where.membershipLevel = membershipLevel;
      }

      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      try {
        const [customers, total] = await Promise.all([
          this.prisma.customer.findMany({
            where,
            skip,
            take: limit,
            orderBy: {
              [sortBy as string]: sortOrder,
            },
            include: {
              createdByUser: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
              _count: {
                select: {
                  sales: true,
                },
              },
            },
          }),
          this.prisma.customer.count({ where }),
        ]);

        return {
          data: customers,
          meta: {
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
            hasNextPage: page < Math.ceil(total / limit),
            hasPreviousPage: page > 1,
          },
        };
      } catch (error) {
        this.logger.error({ err: error, query }, 'Failed to retrieve customers');
        throw error;
      }
    }

  async findOne(id: string) {
      try {
        const customer = await this.prisma.customer.findUnique({
          where: { id },
          include: {
            createdByUser: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            updatedByUser: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            _count: {
              select: {
                sales: true,
              },
            },
          },
        });

        if (!customer) {
          throw new NotFoundException('Pelanggan tidak ditemukan');
        }

        return customer;
      } catch (error) {
        if (!(error instanceof NotFoundException)) {
          this.logger.error({ err: error, customerId: id }, 'Failed to retrieve customer');
        }
        throw error;
      }
    }

  async update(id: string, updateCustomerDto: UpdateCustomerDto, userId: string) {
      const customer = await this.findOne(id);

      // Validate code uniqueness if being updated
      if (updateCustomerDto.code && updateCustomerDto.code !== customer.code) {
        const isUnique = await this.customerCodeGeneratorService.validateCodeUniqueness(
          updateCustomerDto.code,
        );
        if (!isUnique) {
          throw new BadRequestException('Kode pelanggan sudah digunakan');
        }
      }

      // Validate membership number uniqueness if being updated
      if (
        updateCustomerDto.membershipNumber &&
        updateCustomerDto.membershipNumber !== customer.membershipNumber
      ) {
        const existingMembership = await this.prisma.customer.findUnique({
          where: { membershipNumber: updateCustomerDto.membershipNumber },
        });
        if (existingMembership) {
          throw new BadRequestException('Nomor keanggotaan sudah digunakan');
        }
      }

      try {
        return await this.prisma.customer.update({
          where: { id },
          data: {
            ...updateCustomerDto,
            updatedBy: userId,
          },
          include: {
            createdByUser: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
            updatedByUser: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        });
      } catch (error) {
        this.logger.error({
          err: error,
          customerId: id,
          updateFields: Object.keys(updateCustomerDto),
          userId
        }, 'Failed to update customer');
        throw error;
      }
    }

  async remove(id: string) {
      const customer = await this.findOne(id);

      // Check if customer has any sales
      const salesCount = await this.prisma.sale.count({
        where: { customerId: id },
      });

      if (salesCount > 0) {
        throw new BadRequestException(
          'Tidak dapat menghapus pelanggan yang memiliki riwayat transaksi',
        );
      }

      try {
        return await this.prisma.customer.delete({
          where: { id },
        });
      } catch (error) {
        this.logger.error({
          err: error,
          customerId: id,
          customerCode: customer.code
        }, 'Failed to remove customer');
        throw error;
      }
    }

  async deactivate(id: string, userId: string) {
    // Ensure customer exists and get current state
      const customer = await this.prisma.customer.findFirst({where: {id}});

      if(!customer) {
        throw new NotFoundException('Pelanggan tidak ditemukan');
      }

      if (!customer.isActive) {
        throw new BadRequestException('Pelanggan sudah dalam status tidak aktif');
      }

      try {
        return await this.update(id, { isActive: false }, userId);
      } catch (error) {
        this.logger.error({
          err: error,
          customerId: id,
          customerCode: customer.code,
          userId
        }, 'Failed to deactivate customer');
        throw new BadRequestException('Gagal menonaktifkan pelanggan');
      }
    }

  async activate(id: string, userId: string) {
      // Ensure customer exists and get current state
      const customer = await this.prisma.customer.findFirst({where: {id}});

      if(!customer) {
        throw new NotFoundException('Pelanggan tidak ditemukan');
      }

      if (customer.isActive) {
        throw new BadRequestException('Pelanggan sudah dalam status aktif');
      }

      try {
        return await this.update(id, { isActive: true }, userId);
      } catch (error) {
        this.logger.error({
          err: error,
          customerId: id,
          customerCode: customer.code,
          userId
        }, 'Failed to activate customer');
        throw new BadRequestException('Gagal mengaktifkan pelanggan');
      }
    }

  async getStats() {
      try {
        const startTime = Date.now();
        const [total, walkIn, registered, active, inactive] = await Promise.all([
          this.prisma.customer.count(),
          this.prisma.customer.count({ where: { type: 'WALK_IN' } }),
          this.prisma.customer.count({ where: { type: 'REGISTERED' } }),
          this.prisma.customer.count({ where: { isActive: true } }),
          this.prisma.customer.count({ where: { isActive: false } }),
        ]);

        return {
          total,
          walkIn,
          registered,
          active,
          inactive,
        };
      } catch (error) {
        this.logger.error({ err: error }, 'Failed to retrieve customer statistics');
        throw error;
      }
    }
  }
